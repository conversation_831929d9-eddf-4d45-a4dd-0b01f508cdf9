package com.jinghang.capital.core.banks.cybk.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.cycfc.base.vo.Header;
import com.cycfc.base.vo.Image;
import com.cycfc.base.vo.Response;
import com.cycfc.client.api.CycfcAPI;
import com.cycfc.client.util.AESUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jinghang.capital.core.banks.cybk.config.CYBKConfig;
import com.jinghang.capital.core.banks.cybk.config.CYBKImageConfig;
import com.jinghang.capital.core.banks.cybk.convert.CYBKLoanConvert;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.dto.CYBKCommonData;
import com.jinghang.capital.core.banks.cybk.dto.CYBKRemoteRequest;
import com.jinghang.capital.core.banks.cybk.dto.CYBKRemoteResponse;
import com.jinghang.capital.core.banks.cybk.dto.CYBKRequestHeader;
import com.jinghang.capital.core.banks.cybk.dto.CYBKResponseHeader;
import com.jinghang.capital.core.banks.cybk.dto.bind.CYBKBindCardApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.bind.CYBKBindCardApplyResponse;
import com.jinghang.capital.core.banks.cybk.dto.bind.CYBKCardChangeRequest;
import com.jinghang.capital.core.banks.cybk.dto.bind.CYBKCardChangeResponse;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKCreditApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKCreditQueryRequest;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKCreditQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKFileInfo;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKPreCreditRequest;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKPreCreditResponse;
import com.jinghang.capital.core.banks.cybk.dto.file.CYBKCLearVoucherQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.file.CYBKClearVoucherApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.file.CYBKClearVoucherApplyResponse;
import com.jinghang.capital.core.banks.cybk.dto.file.CYBKClearVoucherQueryRequest;
import com.jinghang.capital.core.banks.cybk.dto.limit.CYBKLimitAdjustApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.limit.CYBKLimitAdjustApplyResponse;
import com.jinghang.capital.core.banks.cybk.dto.limit.CYBKLimitApplyQueryRequest;
import com.jinghang.capital.core.banks.cybk.dto.limit.CYBKLimitApplyQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.limit.CYBKLimitQueryRequest;
import com.jinghang.capital.core.banks.cybk.dto.limit.CYBKLimitQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanApplyResponse;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanQueryRequest;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLprQueryRequest;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLprQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.repay.CYBKRepayApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.repay.CYBKRepayApplyResponse;
import com.jinghang.capital.core.banks.cybk.dto.repay.CYBKRepayPlanQueryRequest;
import com.jinghang.capital.core.banks.cybk.dto.repay.CYBKRepayPlanQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.repay.CYBKRepayQueryRequest;
import com.jinghang.capital.core.banks.cybk.dto.repay.CYBKRepayQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.repay.CYBKRepayTrialRequest;
import com.jinghang.capital.core.banks.cybk.dto.repay.CYBKRepayTrialResponse;
import com.jinghang.capital.core.banks.cybk.enums.CYBKFileType;
import com.jinghang.capital.core.banks.cybk.enums.CybkResponseStatus;
import com.jinghang.capital.core.entity.LoanFile;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.service.FileService;
import com.jinghang.capital.core.util.IdGenUtil;
import com.jinghang.common.http.HttpFramework;
import com.jinghang.common.http.HttpRequest;
import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.IdGen;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.common.util.crypt.DigestUtil;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class CYBKRequestService {
    /**
     * 放款申请 超时时间设置为20秒
     */
    public static final Duration LOAN_APPLY_REQUEST_TIMEOUT = Duration.ofSeconds(20);
    private static final Logger logger = LoggerFactory.getLogger(CYBKRequestService.class);
    private static final int MAX_ID_LENGTH = 32;
    private static final String CODE_SUCCESS = "0000";
    private final CYBKConfig config;
    private final CYBKImageConfig tcpConfig;

    @Autowired
    private FileService fileService;

    @Autowired
    public CYBKRequestService(CYBKConfig config, CYBKImageConfig tcpConfig) {
        this.config = config;
        this.tcpConfig = tcpConfig;
    }

    public <R> R request(CYBKBaseRequest request, Class<R> respClass) throws HttpException, JsonProcessingException {
        //公告参数转换
        CYBKCommonData commonRequest = fillCommonParams(request);

        String bodyStr = JsonUtil.convertToString(commonRequest);
        logger.info("长银直连, 加密后请求报文: {}", bodyStr);

        String serverUrl = this.config.getCybkServerUrl() + request.getTradeCode().getTradeCode() + config.getShortCode();
        HttpRequest post = HttpRequest.post(HttpFramework.HTTPCLIENT5, serverUrl);

        post.body(bodyStr);

        String resp = HttpUtil.exec(post);
        logger.info("长银直连, 解密前返回报文: {}", resp);

        CYBKCommonData commonData = JsonUtil.convertToObject(resp, CYBKCommonData.class);
        if (commonData == null || StringUtil.isBlank(commonData.getJson())) {
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR.getCode(), "长银消金直连,请求失败,返回参数为空");
        }

        //返回报文解密
        String decrypted = AESUtil.decrypt(commonData.getJson(), config.getContentKey());
        logger.info("长银直连：{},\n请求参数：{}\n解密后返回报文: {}", serverUrl, JSON.toJSONString(commonRequest), decrypted);

        //验签
        boolean verify = verify(commonData, decrypted);
        if (!verify) {
            throw new BizException(BizErrorCode.RSA_SIGN_ERROR);
        }

        CYBKRemoteResponse response = JsonUtil.convertToObject(decrypted, CYBKRemoteResponse.class);
        CYBKResponseHeader respHead = response.getHead();
        if (!respHead.getRespCode().equals(CODE_SUCCESS)) {
            throw new BizException(respHead.getRespCode(), respHead.getRespMsg());
        }

        return JsonUtil.convertToObject(JsonUtil.convertToString(response.getBody()), respClass);
    }

    public CYBKRemoteResponse requestWithDuration(CYBKBaseRequest request, Duration timeoutSecond) throws HttpException, JsonProcessingException {
        //公告参数转换
        CYBKCommonData commonRequest = fillCommonParams(request);

        String bodyStr = JsonUtil.convertToString(commonRequest);
        logger.info("长银直连, 加密后请求报文: {}", bodyStr);

        String serverUrl = this.config.getCybkServerUrl() + request.getTradeCode().getTradeCode() + config.getShortCode();
        HttpRequest post = HttpRequest.post(HttpFramework.HTTPCLIENT5, serverUrl);
        if (Objects.nonNull(timeoutSecond)) {
            post.timeout(timeoutSecond);
        }
        post.body(bodyStr);

        String resp = HttpUtil.exec(post);
        logger.info("长银直连, 解密前返回报文: {}", resp);

        CYBKCommonData commonData = JsonUtil.convertToObject(resp, CYBKCommonData.class);
        if (commonData == null || StringUtil.isBlank(commonData.getJson())) {
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR.getCode(), "长银消金直连,请求失败,返回参数为空");
        }

        //返回报文解密
        String decrypted = AESUtil.decrypt(commonData.getJson(), config.getContentKey());
        logger.info("长银直连, 解密后返回报文: {}", decrypted);

        //验签
        boolean verify = verify(commonData, decrypted);
        if (!verify) {
            throw new BizException(BizErrorCode.RSA_SIGN_ERROR);
        }

        return JsonUtil.convertToObject(decrypted, CYBKRemoteResponse.class);
    }


    /**
     * 影像文件、签章协议下载
     *
     * @param fileInfo
     * @return
     */
    public String downloadImage(CYBKFileInfo fileInfo) {
        Header header = new Header();
        header.setChannel(config.getChannel()); //渠道编号--由长银分配
        header.setAppID(config.getAppid()); //接入方系统ID-有长银分配
        header.setReqNo(UUID.randomUUID().toString()); //请求方流水号-保证唯一

        // 报文body内容
        Image image = CYBKLoanConvert.INSTANCE.toImage(fileInfo);
        image.setChannelId(config.getChannelId()); //渠道编号--由长银分配
        logger.info("长银直连, 下载影像文件请求报文: head:{},image:{}", JSONObject.toJSONString(header), JSONObject.toJSONString(image));
        //下载影像文件
        Response response = null;
        try {
            response = CycfcAPI.downLoadImage(header, image);
            logger.info("长银直连, 下载影像文件 响应报文: {}", JSONObject.toJSONString(response));
        } catch (Exception ex) {
            logger.error("cybk download image error.", ex);
        }

        if (response != null && Objects.nonNull(response.getRespBody().get("data"))) {
            //处理成功
            return response.getRespBody().get("data");
        } else {
            //错误消息
            String errorMsg = response != null ? response.getRespMsg() : "返回参数为null";
            throw new BizException(BizErrorCode.CONTRACT_DOWNLOAD_ERROR.getCode(), errorMsg);
        }
    }


    /**
     * 发送影像文件
     *
     * @param loanFile
     * @param idNo
     */
    public String sendImage(LoanFile loanFile, String idNo) {
        logger.info("长银 影像文件上传,Host:{}, port:{}，AesKey{}", tcpConfig.getCybkImageHost(), tcpConfig.getCybkImagePort(), tcpConfig.getCybkImageAesKey());
        CycfcAPI.instance(tcpConfig.getCybkImageHost(), tcpConfig.getCybkImagePort(), tcpConfig.getCybkImageAesKey(), tcpConfig.getCybkImagePrivateKey());
        // 设置报文头信息
        Header header = new Header();
        header.setChannel(config.getChannel()); //渠道编号--由长银分配
        header.setAppID(config.getAppid()); //接入方系统ID-有长银分配
        header.setReqNo(UUID.randomUUID().toString()); //请求方流水号-保证唯一
        // 设置报文内容
        Image image = new Image();
        image.setApplCde(IdGenUtil.genReqNo()); //长银流水号
        image.setChannelId(config.getChannelId()); //渠道编号--由长银分配
        image.setApplyTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))); //申请时间
        CYBKFileType fileType = CYBKFileType.getEnumByFileType(loanFile.getFileType());
        image.setFileName(CYBKFileType.getEnumByFileType(loanFile.getFileType()).getFileName()); //文件名称
        image.setIdNo(idNo); //身份证号
        image.setOutApplSeq(IdGenUtil.genReqNo());
        image.setImgType(fileType.getCode()); //影像类型编码,文档约定中有
        String suffix = loanFile.getOssKey().substring(loanFile.getOssKey().lastIndexOf(".") + 1);
        image.setFileType(suffix); //文件类型
        //若Attachments为空，则LocalFilePath不能为空，否则程序无法找到本地文件
        byte[] ossFileBytes = fileService.getOssFileBytes(loanFile.getOssBucket(), loanFile.getOssKey());
        image.setFileBytes(ossFileBytes);
        long start = System.currentTimeMillis();
        Response response = new Response();
        try {
            logger.info("长银直连, 影像文件上传, header:{}, image:{}", response, image);
            response = CycfcAPI.submitImagePre(header, image);
        } catch (Exception e) {
            logger.info("长银直连, 影像文件上传, response:{}, e:{}", response, e.getMessage());
            //            logger.error("长银直连, 影像文件上传,image:{},e:",image,e);
            //            for (int i = 0; i < config.getRetries(); i++) {
            //                logger.error("长银直连, 影像文件上传失败重试,image:{},retries:{}:",image,i);
            //                try {
            //                    if (Objects.isNull(response)||Objects.isNull(response.getRespBody())
            //                    ||StringUtils.isBlank(response.getRespBody().get("imageId"))) {
            //                        response = CycfcAPI.submitImagePre(header, image);
            //                    }else{
            //                       if (StringUtils.isNotBlank(response.getRespBody().get("imageId"))) {
            //                           break;
            //                       }
            //                    }
            //                }catch (Exception e1) {
            //                    logger.error("长银直连, 影像文件上传失败重试继续失败,image:{},e:",image,e);
            //                }
            //            }
        }
        long spent = System.currentTimeMillis() - start;
        logger.info("长银直连, 影像文件上传, output:{}, spent:{}", response, spent);
        return response.getRespBody().get("imageId");
    }

    // 预授信申请调用长银接口
    public CYBKPreCreditResponse preCreditApply(CYBKPreCreditRequest request) {
        CYBKPreCreditResponse response = null;
        try {
            response = this.request(request, CYBKPreCreditResponse.class);
        } catch (HttpException e) {
            logger.error("长银直连, 预授信申请, 请求失败, 请求报文: {}, 响应报文: {}", request, response);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        } catch (JsonProcessingException e) {
            logger.error("Json转换异常", e);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        }
        return response;
    }


    public CYBKRemoteResponse creditApply(CYBKCreditApplyRequest request) {
        try {
            return this.requestWithDuration(request, null);
        } catch (JsonProcessingException jpe) {
            logger.error("Json转换异常", jpe);
        } catch (HttpException he) {
            logger.error("Http请求异常", he);
        } catch (BizException bizEx) {
            logger.error("请求长银直连异常", bizEx);
        }
        return null;
    }


    public CYBKCreditQueryResponse creditQuery(CYBKCreditQueryRequest request) {
        CYBKCreditQueryResponse response = null;
        try {
            response = this.request(request, CYBKCreditQueryResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        }

        return response;
    }

    public CYBKLimitQueryResponse limitQuery(CYBKLimitQueryRequest request) {
        CYBKLimitQueryResponse response = null;
        try {
            response = this.request(request, CYBKLimitQueryResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("limitQuery-Json转换异常", jpe);
        } catch (HttpException he) {
            logger.error("limitQuery-Http请求异常", he);
        } catch (BizException bizEx) {
            logger.error("limitQuery-请求长银直连异常", bizEx);
        }
        return response;

    }

    public CYBKLimitAdjustApplyResponse applyAdjustQuota(CYBKLimitAdjustApplyRequest request) {
        CYBKLimitAdjustApplyResponse response = null;
        try {
            response = this.request(request, CYBKLimitAdjustApplyResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("applyAdjustQuota-Json转换异常", jpe);
        } catch (HttpException he) {
            logger.error("applyAdjustQuota-Http请求异常", he);
        } catch (BizException bizEx) {
            logger.error("applyAdjustQuota-请求长银直连异常", bizEx);
        }
        return response;

    }

    public CYBKLimitApplyQueryResponse quotaAdjustQuery(CYBKLimitApplyQueryRequest request) {
        CYBKLimitApplyQueryResponse response = null;
        try {
            response = this.request(request, CYBKLimitApplyQueryResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("quotaAdjustQuery-Json转换异常", jpe);
        } catch (HttpException he) {
            logger.error("quotaAdjustQuery-Http请求异常", he);
        } catch (BizException bizEx) {
            logger.error("quotaAdjustQuery-请求长银直连异常", bizEx);
        }
        return response;

    }

    /**
     * 银行卡签约申请
     *
     * @param request
     * @return
     */
    public CYBKBindCardApplyResponse bindApply(CYBKBindCardApplyRequest request) {
        CYBKBindCardApplyResponse response = null;
        try {
            response = this.request(request, CYBKBindCardApplyResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("长银直连,绑卡协议同步失败", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("长银直连,绑卡协议同步失败", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        }
        return response;
    }

    /**
     * 卡号变更通知
     *
     * @param request
     * @return
     */
    public CYBKCardChangeResponse cardChangeNotify(CYBKCardChangeRequest request) {
        CYBKCardChangeResponse response = null;
        try {
            response = this.request(request, CYBKCardChangeResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("长银直连,卡号变更通知失败", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("长银直连,卡号变更通知失败", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        }
        return response;
    }

    public CYBKLoanApplyResponse loanApply(CYBKLoanApplyRequest request) {
        try {

            CYBKRemoteResponse response = this.requestWithDuration(request, LOAN_APPLY_REQUEST_TIMEOUT);

            CYBKResponseHeader respHead = response.getHead();
            if (!respHead.getRespCode().equals(CODE_SUCCESS)) {
                logger.error("长银直连放款申请,同步响应异常 loanId:{}, response{}", request.getOutLoanSeq(), JsonUtil.convertToString(response));
                if (Objects.isNull(response.getBody())) {
                    return null;
                }
            }

            return JsonUtil.convertToObject(JsonUtil.convertToString(response.getBody()), CYBKLoanApplyResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("请求长银直连异常 json转换失败", jpe);
        } catch (HttpException he) {
            logger.error("请求长银直连 Http异常", he);
        } catch (BizException bizEx) {
            logger.error("请求长银直连异常", bizEx);
        } catch (Exception ex) {
            logger.error("请求长银直连异常", ex);
        }

        return null;
    }


    public CYBKLoanQueryResponse loanQuery(CYBKLoanQueryRequest request) {
        CYBKLoanQueryResponse response = null;
        try {
            response = this.request(request, CYBKLoanQueryResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        }

        return response;
    }


    public CYBKRepayPlanQueryResponse repayPlanQuery(CYBKRepayPlanQueryRequest request) {
        CYBKRepayPlanQueryResponse response = null;
        try {
            response = this.request(request, CYBKRepayPlanQueryResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        }

        return response;
    }


    public CYBKRepayTrialResponse repayTrial(CYBKRepayTrialRequest request) {
        try {
            return this.request(request, CYBKRepayTrialResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("长银直连结清试算失败", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("长银直连结清试算失败", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        } catch (BizException ex) {
            logger.error("长银直连结清试算失败", ex);
            throw ex;
        } catch (Exception ex) {
            logger.error("长银直连结清试算失败", ex);
            throw new BizException(BizErrorCode.REPAY_TRIAL_ERROR);
        }
    }


    public CYBKRepayApplyResponse repayApply(CYBKRepayApplyRequest request) {
        try {
            return this.request(request, CYBKRepayApplyResponse.class);
        } catch (Exception bizEx) {
            logger.error("长银直连 还款申请失败", bizEx);
        }
        return new CYBKRepayApplyResponse();
    }


    public CYBKRepayQueryResponse repayQuery(CYBKRepayQueryRequest request) {
        try {
            return this.request(request, CYBKRepayQueryResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("长银直连 还款结果查询失败", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("长银直连 还款结果查询失败", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        } catch (BizException e) {
            if (CybkResponseStatus.NOT_EXIST.getRespCode().equals(e.getCode())) {
                CYBKRepayQueryResponse response = new CYBKRepayQueryResponse();
                response.setBillStatus("02");
                response.setFailReason("还款不存在");
                return response;
            }
            logger.error("长银直连 还款结果查询失败", e);
            throw e;
        }
    }

    /**
     * lpr查询
     *
     * @param request 请求
     * @return 返回结果
     */
    public CYBKLprQueryResponse lprQuery(CYBKLprQueryRequest request) {
        CYBKLprQueryResponse response = null;
        try {
            response = this.request(request, CYBKLprQueryResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        }

        return response;
    }


    /**
     * 结清证明申请
     *
     * @param request 请求
     * @return 返回结果
     */
    public CYBKClearVoucherApplyResponse clearVoucherApply(CYBKClearVoucherApplyRequest request) {
        CYBKClearVoucherApplyResponse response = null;
        try {
            response = this.request(request, CYBKClearVoucherApplyResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        }
        return response;
    }

    /**
     * 结清证明结果查询
     *
     * @param request 请求
     * @return 返回结果
     */
    public CYBKCLearVoucherQueryResponse clearVoucherQuery(CYBKClearVoucherQueryRequest request) {
        CYBKCLearVoucherQueryResponse response = null;
        try {
            response = this.request(request, CYBKCLearVoucherQueryResponse.class);
        } catch (JsonProcessingException jpe) {
            logger.error("", jpe);
            throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
        } catch (HttpException he) {
            logger.error("", he);
            throw new BizException(BizErrorCode.HTTP_REQUEST_ERROR);
        }
        return response;
    }

    public boolean verify(CYBKCommonData resp, String decryptedContent) {
        try {
            String decryptedSign = AESUtil.decrypt(resp.getSign(), config.getSignKey());
            String md5 = DigestUtil.md5(decryptedContent);
            return md5.equalsIgnoreCase(decryptedSign);
        } catch (Exception e) {
            logger.error("", e);
            throw new BizException(BizErrorCode.RSA_SIGN_ERROR);
        }
    }

    private CYBKCommonData fillCommonParams(CYBKBaseRequest req) throws JsonProcessingException {
        CYBKRequestHeader requestHead = new CYBKRequestHeader();
        requestHead.setChannel(config.getChannel());
        requestHead.setReqNo(IdGen.genId("CY", MAX_ID_LENGTH - 2));
        requestHead.setAppID(config.getAppid());
        requestHead.setReqDateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        requestHead.setVersion("01");

        CYBKRemoteRequest<CYBKBaseRequest> request = new CYBKRemoteRequest<>();
        request.setHead(requestHead);
        request.setBody(req);

        String requestStr = JsonUtil.convertToString(request);
        String md5 = DigestUtil.md5(requestStr);
        logger.info("长银直连, 加密前请求报文: {}", requestStr);
        logger.info("长银直连, md5: {}", md5);

        // 签名加密
        String encryptSign = AESUtil.encrypt(md5, config.getSignKey());
        // 报文内容加密
        String encrypt = AESUtil.encrypt(requestStr, config.getContentKey());

        CYBKCommonData baseRemoteData = new CYBKCommonData();
        baseRemoteData.setChannel(config.getChannel());
        baseRemoteData.setKey("1");
        baseRemoteData.setJson(encrypt);
        baseRemoteData.setSign(encryptSign);
        baseRemoteData.setRandomStr(IdGen.genId("CYRDM", MAX_ID_LENGTH));
        return baseRemoteData;
    }


}
