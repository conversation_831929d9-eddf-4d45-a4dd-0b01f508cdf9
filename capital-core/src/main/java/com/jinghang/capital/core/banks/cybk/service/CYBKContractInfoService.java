package com.jinghang.capital.core.banks.cybk.service;


import com.jinghang.capital.core.banks.AbstractBankContractInfoService;
import com.jinghang.capital.core.entity.Account;
import com.jinghang.capital.core.entity.AccountBankCard;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.enums.AgreementType;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.repository.AccountBankCardRepository;
import com.jinghang.capital.core.repository.AccountRepository;
import com.jinghang.capital.core.repository.CreditRepository;
import com.jinghang.capital.core.repository.LoanRepository;
import com.jinghang.capital.core.service.remote.nfsp.sign.req.AuthDto;
import com.jinghang.capital.core.service.remote.nfsp.sign.req.SignApplyReq;
import java.time.Instant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CYBKContractInfoService extends AbstractBankContractInfoService {

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private AccountBankCardRepository accountBankCardRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private AccountRepository accountRepository;


    @Override
    public SignApplyReq fetchDynamicSignParam(String businessId, AgreementType agreementType) {
        return null;
    }

    @Override
    public SignApplyReq fetchTemplateSignParam(String businessId, AgreementType agreementType) {
        LoanStage stage = agreementType.getLoanStage();
        return switch (stage) {
            case CREDIT -> getCreditTemplateParam(businessId, agreementType);
            case LOAN -> getLoanTemplateParam(businessId, agreementType);
            default -> null;
        };
    }

    private SignApplyReq getLoanTemplateParam(String businessId, AgreementType agreementType) {
        return getLoanParam(businessId, agreementType);
    }

    private SignApplyReq getLoanParam(String businessId, AgreementType agreementType) {
        Loan loan = loanRepository.findById(businessId).orElseThrow();
        Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();
        Account account = accountRepository.findById(credit.getAccountId()).orElseThrow();
        //        List<AccountBankCard> byCardNoS = accountBankCardRepository.findByCardNo(loan.getLoanCardId());
        //        AccountBankCard accountBankCard = byCardNoS.get(0);

        AccountBankCard accountBankCard = accountBankCardRepository.findById(loan.getLoanCardId()).orElseThrow();
        SignApplyReq signApplyReq = new SignApplyReq();
        signApplyReq.setContractCode(agreementType.getTemplateNo());
        signApplyReq.setTrafficCode(loan.getFlowChannel().name());
        signApplyReq.setAuthNo(loan.getLoanNo());
        signApplyReq.setCreditId(businessId);
        signApplyReq.setAcctName(loan.getCustName());
        signApplyReq.setAddress(account.getLivingAddress());
        AuthDto authDto = new AuthDto();
        authDto.setCardNo(loan.getCustCertNo());
        authDto.setCardType("CRED_PSN_CH_IDCARD");
        authDto.setName(loan.getCustName());
        long epochMilli = Instant.now().toEpochMilli();
        authDto.setTimeStamp(epochMilli);
        signApplyReq.setAuthDto(authDto);
        signApplyReq.setBankName(accountBankCard.getBankName());
        signApplyReq.setBankCardNo(accountBankCard.getCardNo());
        signApplyReq.setPhone(loan.getCustMobile());
        return signApplyReq;
    }

    private SignApplyReq getCreditTemplateParam(String businessId, AgreementType agreementType) {
        Credit credit = creditRepository.findById(businessId).orElseThrow();
        AccountBankCard accountBankCard = accountBankCardRepository.findById(credit.getCardId()).orElseThrow();
        Account account = accountRepository.findById(credit.getAccountId()).orElseThrow();
        SignApplyReq signApplyReq = new SignApplyReq();
        signApplyReq.setContractCode(agreementType.getTemplateNo());
        signApplyReq.setTrafficCode(credit.getFlowChannel().name());
        signApplyReq.setAddress(account.getLivingAddress());
        signApplyReq.setAuthNo(credit.getCreditNo());
        signApplyReq.setCreditId(businessId);
        signApplyReq.setAcctName(credit.getCustName());
        AuthDto authDto = new AuthDto();
        authDto.setCardNo(credit.getCustCertNo());
        authDto.setCardType("CRED_PSN_CH_IDCARD");
        authDto.setName(credit.getCustName());
        long epochMilli = Instant.now().toEpochMilli();
        authDto.setTimeStamp(epochMilli);
        signApplyReq.setAuthDto(authDto);
        signApplyReq.setBankName(accountBankCard.getCardName());
        signApplyReq.setBankCardNo(accountBankCard.getCardNo());
        signApplyReq.setPhone(credit.getCustMobile());
        return signApplyReq;
    }

    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.CYBK == channel;
    }
}
