package com.jinghang.capital.core.banks.cybk.service;

import com.alibaba.fastjson2.JSONObject;
import com.jinghang.capital.core.banks.AbstractBankLoanService;
import com.jinghang.capital.core.banks.cybk.config.CYBKConfig;
import com.jinghang.capital.core.banks.cybk.convert.CYBKLoanConvert;
import com.jinghang.capital.core.banks.cybk.dto.CYBKCallBackCommonResult;
import com.jinghang.capital.core.banks.cybk.dto.bind.CYBKBindCardApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.bind.CYBKBindCardApplyResponse;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKAccountInfo;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKAddress;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKFamilyInfo;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKImageInfo;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKRelationInfo;
import com.jinghang.capital.core.banks.cybk.dto.loan.BasicInfoReq;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanApplyResponse;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanGuaranteeInfo;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanGuaranteePlan;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanQueryRequest;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLprQueryRequest;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLprQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.loan.ExtendInfo;
import com.jinghang.capital.core.banks.cybk.dto.repay.CYBKRepayPlanQueryRequest;
import com.jinghang.capital.core.banks.cybk.dto.repay.CYBKRepayPlanQueryResponse;
import com.jinghang.capital.core.banks.cybk.enums.CYBKContactType;
import com.jinghang.capital.core.banks.cybk.enums.CYBKFileType;
import com.jinghang.capital.core.banks.cybk.enums.CYBKLoanPurpose;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;
import com.jinghang.capital.core.banks.cybk.remote.CYBKRequestService;
import com.jinghang.capital.core.dto.ContractBizDto;
import com.jinghang.capital.core.entity.Account;
import com.jinghang.capital.core.entity.AccountBankCard;
import com.jinghang.capital.core.entity.AccountContactInfo;
import com.jinghang.capital.core.entity.AgreementSignature;
import com.jinghang.capital.core.entity.CYBKBankList;
import com.jinghang.capital.core.entity.CYBKCreditFlow;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanFile;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.enums.AgreementType;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.enums.LoanStatus;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.enums.SignatureType;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.AccountBankCardRepository;
import com.jinghang.capital.core.repository.AccountContactInfoRepository;
import com.jinghang.capital.core.repository.AccountRepository;
import com.jinghang.capital.core.repository.AgreementSignatureRepository;
import com.jinghang.capital.core.repository.CYBKBankRepository;
import com.jinghang.capital.core.repository.CYBKCreditFlowRepository;
import com.jinghang.capital.core.repository.CreditRepository;
import com.jinghang.capital.core.repository.LoanReplanRepository;
import com.jinghang.capital.core.service.FileService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.loan.FinLoanService;
import com.jinghang.capital.core.util.IdGenUtil;
import com.jinghang.capital.core.vo.loan.LoanApplyVo;
import com.jinghang.capital.core.vo.loan.LoanLprResultVo;
import com.jinghang.capital.core.vo.loan.LoanQueryVo;
import com.jinghang.capital.core.vo.loan.LoanResultVo;
import com.jinghang.capital.core.vo.repay.PlanItemVo;
import com.jinghang.capital.core.vo.repay.PlanVo;
import com.jinghang.capital.core.vo.repay.RepayDateApplyVo;
import com.jinghang.capital.core.vo.repay.RepayDateResultVo;
import com.jinghang.common.loan.PlanGenerator;
import com.jinghang.common.loan.plan.InterestType;
import com.jinghang.common.loan.plan.RepayPlan;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @date 2023/8/20
 */
@Service
@Qualifier("CYBKLoanService")
public class CYBKLoanService extends AbstractBankLoanService {

    public static final int SIX = 6;
    public static final int SEVEN = 7;
    public static final int LOAN_STAGE_FILE_NUM = 1;
    private static final Logger logger = LoggerFactory.getLogger(CYBKLoanService.class);
    private static final DateTimeFormatter VALID_FORMAT = DateTimeFormatter.ofPattern("HHmm");
    private static final int PERCENT = 100;
    private static final int TWELVE = 12;
    private static final BigDecimal CUSTOM_YEAR_DAYS = BigDecimal.valueOf(365);
    private static final int PROCESSING_POINT_NUM = 6;
    private static final LocalDate ID_CARD_EXPIRE_PERMANENT = LocalDate.of(2099, 1, 1);
    private static final LocalDate CYBK_PERMANENT = LocalDate.of(2099, 12, 31);
    /**
     * 客户风险等级计算
     * <p>
     * A:(400,1000] B:(300,400] C:(200,300] D:(0,200]
     *
     * @param acardScore
     * @return
     */
    private final int oneThousand = 1000;
    private final int fourHundred = 400;
    private final int threeHundred = 300;
    private final int twoHundred = 200;
    private CYBKRequestService requestService;
    private FileService fileService;
    private CYBKConfig cybKConfig;
    private CreditRepository creditRepository;
    private CYBKCreditFlowRepository cybKCreditFlowRepository;
    private CYBKBankRepository cybkBankRepository;
    private AccountRepository accountRepository;
    private AccountBankCardRepository accountBankCardRepository;
    private AgreementSignatureRepository agreementSignatureRepository;
    private AccountContactInfoRepository accountContactInfoRepository;
    @Autowired
    private CYBKRequestService cybkRequestService;
    @Autowired
    private MqService mqService;
    @Autowired
    private FinLoanService finLoanService;
    @Autowired
    private WarningService warningService;
    private LoanReplanRepository loanReplanRepository;
    @Autowired
    private CYBKConfig config;

    @Override
    protected boolean bankLoanValidate(LoanApplyVo apply) {
        LocalTime actTime = LocalTime.now();
        if (!isValidTime(actTime)) {
            throw new BizException(BizErrorCode.LOAN_TIME_INVALID);
        }
        return true;
    }

    private boolean isValidTime(LocalTime actTime) {
        LocalTime start = LocalTime.parse(cybKConfig.getLoanStartTime(), VALID_FORMAT);
        LocalTime end = LocalTime.parse(cybKConfig.getLoanEndTime(), VALID_FORMAT);
        return actTime.isAfter(start) && actTime.isBefore(end);
    }

    private String syncBankProtocol(AccountBankCard accountBankCard) {
        // 同步扣款协议号
        CYBKBindCardApplyRequest request = new CYBKBindCardApplyRequest();
        request.setOutSignSeq(IdGenUtil.genReqNo("GX"));
        request.setPayMode("1"); //银行卡
        request.setSignNo(accountBankCard.getAgreeNo());
        request.setIdType("20"); //身份证
        request.setIdNo(accountBankCard.getCertNo());
        request.setTelNo(accountBankCard.getPhone());
        request.setAcctNo(accountBankCard.getCardNo());
        request.setAcctName(accountBankCard.getCardName());
        request.setPayChannel(cybKConfig.getRepayChannel());
        request.setSignSts("00");

        CYBKBankList cybkBank = cybkBankRepository.findByAbbrIgnoreCase(accountBankCard.getBankCode());
        request.setAcctBankCode(cybkBank.getCycfcCode());

        CYBKBindCardApplyResponse response = requestService.bindApply(request);
        if (!"1".equals(response.getStatus())) {
            getWarningService().warn("长银直连,绑卡协议同步失败:" + response.getStatusDesc(), logger::error);
            throw new BizException(BizErrorCode.BANK_CODE_SIGN_APPLY.getCode(), "长银直连,共享绑卡协议失败");
        }
        String signSeq = response.getSignSeq();
        return signSeq;
    }

    private CYBKLoanApplyRequest buildApplyRequest(Loan loan, AccountBankCard accountBankCard, List<CYBKImageInfo> cybkImageInfos, String signSeq) {
        //委托保证合同的合同编号 loan保存
        String guaranteeContractNo = IdGenUtil.genReqNo("GCN");
        loan.setGuaranteeContractNo(guaranteeContractNo);
        Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();
        CYBKCreditFlow creditFlow = cybKCreditFlowRepository.findByCreditId(credit.getId()).orElseThrow();
        Account account = accountRepository.findById(credit.getAccountId()).orElseThrow();
        CYBKLoanApplyRequest request = new CYBKLoanApplyRequest();
        request.setOutLoanSeq(loan.getId());
        request.setOutContractSeq(loan.getLoanApplyContractNo()); //目前是业务系统的LoanId
        request.setApplCde(credit.getCreditNo());
        request.setCustId(creditFlow.getCustId());
        request.setDnAmt(credit.getLoanAmt());
        request.setApplyTnr(new BigDecimal(loan.getPeriods()));
        request.setMtdCde("SYS002"); //等额本息
        CYBKLoanPurpose loanPurpose = CYBKLoanPurpose.getEnumByLoanPurpose(loan.getLoanPurpose());
        request.setPurpose(loanPurpose.getCode());
        request.setOtherPurpose(loanPurpose.getDesc());
        request.setPriceIntRat(loan.getBankRate());
        request.setAgrSeq(signSeq);
        request.setMtdMode("FX"); //固定利率
        //request.setRepcOpt("DDA"); //满一年调整
        request.setCustDayRate(loan.getCustomRate());
        request.setLoanFreq("1M"); //1个月
        request.setDueDayOpt("1"); //放款日对日
        request.setTerminalType(config.getMerchantTerminal());
        request.setMerchantNo(config.getMerchantCode());
        request.setStoreCode(config.getMerchantShop());
        request.setContSignDt(LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE)); //合同签订日期
        //回调地址
        request.setCallbackUrl(config.getCallBackUrl() + CYBKTradeCode.LOAN_APPLY_CALL_BACK.getTradeCode());

        // 放款卡信息
        CYBKBankList cybkBank = cybkBankRepository.findByAbbrIgnoreCase(accountBankCard.getBankCode());
        CYBKAccountInfo accountInfo1 = new CYBKAccountInfo();
        accountInfo1.setAcctKind("01"); //放款账号
        accountInfo1.setAcctTyp("01"); //个人账户
        accountInfo1.setAcctBankCode(cybkBank.getCycfcCode());
        accountInfo1.setAcctNo(accountBankCard.getCardNo());
        accountInfo1.setAcctName(accountBankCard.getCardName());
        accountInfo1.setIdTyp("20");
        accountInfo1.setIdNo(accountBankCard.getCertNo());
        accountInfo1.setAcctPhone(accountBankCard.getPhone());
        CYBKAccountInfo accountInfo2 = new CYBKAccountInfo();
        accountInfo2.setAcctKind("02"); //还款账号
        accountInfo2.setAcctTyp("01"); //个人账户
        accountInfo2.setAcctBankCode(cybkBank.getCycfcCode());
        accountInfo2.setAcctNo(accountBankCard.getCardNo());
        accountInfo2.setAcctName(accountBankCard.getCardName());
        accountInfo2.setIdTyp("20");
        accountInfo2.setIdNo(accountBankCard.getCertNo());
        accountInfo2.setAcctPhone(accountBankCard.getPhone());

        //主申人信息
        BasicInfoReq basicInfoReq = new BasicInfoReq();
        basicInfoReq.setIdNo(accountBankCard.getCertNo());
        basicInfoReq.setIdNoStartDate(account.getCertValidStart().format(DateTimeFormatter.ISO_LOCAL_DATE));
        basicInfoReq.setIdNoEndDate(calcCertValidDate(account.getCertValidEnd()).format(DateTimeFormatter.ISO_LOCAL_DATE));
        //家庭
        CYBKAddress liveAddress = new CYBKAddress();
        liveAddress.setProvince(account.getLivingProvinceCode());
        liveAddress.setCity(account.getLivingCityCode());
        liveAddress.setArea(account.getLivingDistrictCode());
        liveAddress.setAddress(account.getLivingAddress());
        CYBKAddress regAddInfo = new CYBKAddress();
        regAddInfo.setProvince(account.getProvinceCode());
        regAddInfo.setCity(account.getCityCode());
        regAddInfo.setArea(account.getDistrictCode());
        regAddInfo.setAddress(account.getCertAddress());
        CYBKFamilyInfo familyInfo = new CYBKFamilyInfo();
        familyInfo.setLiveAddInfo(liveAddress);
        familyInfo.setRegAddInfo(regAddInfo);

        // 扩展信息
        ExtendInfo extendInfo = new ExtendInfo();
        extendInfo.setCustSource(credit.getApplyChannel());
        // 联系人
        List<CYBKRelationInfo> contactInfos = new ArrayList<>();
        List<AccountContactInfo> accountContactInfos = accountContactInfoRepository.findByAccountId(account.getId());
        accountContactInfos.forEach(c -> {
            CYBKRelationInfo contactInfo = new CYBKRelationInfo();
            contactInfo.setRelName(c.getName());
            contactInfo.setRelMobile(c.getPhone());
            contactInfo.setRelRelation(CYBKContactType.getCodeByRelation(c.getRelation()));
            contactInfos.add(contactInfo);
        });
        request.setFamilyInfo(familyInfo);
        request.setRelationList(contactInfos);
        request.setBasicInfo(basicInfoReq);
        request.setAccInfoList(List.of(accountInfo1, accountInfo2));
        // 新增扩展信息
        request.setExtendInfo(extendInfo);
        //request.setGuaranteeInfo(guaranteeInfo);
        // List<CYBKImageInfo> cybkImageInfos = uploadFile(loanFiles, accountBankCard.getCertNo());
        //协议文件
        request.setImageInfoList(cybkImageInfos);
        logger.info("长银直连放款申请请求参数: {}", JsonUtil.toJsonString(request));
        return request;
    }

    List<CYBKImageInfo> uploadFile(List<LoanFile> creditFiles, String idNo) {
        List<CYBKImageInfo> imageInfoList = new ArrayList<>();
        creditFiles.forEach(lf -> {
            CYBKFileType fileType = CYBKFileType.getEnumByFileType(lf.getFileType());
            if (Objects.nonNull(fileType)) {
                CYBKImageInfo imageInfo = new CYBKImageInfo();
                imageInfo.setImageStage("3"); //放款阶段
                imageInfo.setImageName(fileType.getFileName());
                imageInfo.setImageType(fileType.getCode());
                String respNo = "";
                try {
                    logger.info("长银文件上传放款, 组装后参数: {}", JsonUtil.toJsonString(lf));
                    respNo = cybkRequestService.sendImage(lf, idNo);
                } catch (Exception e) {
                    logger.error("长银文件上传放款信息异常：{}", e.getMessage());
                }
                imageInfo.setImageUrl(respNo);
                imageInfoList.add(imageInfo);
            }
        });
        return imageInfoList;
    }

    /**
     * 证件有效转换
     *
     * @param expireDay
     * @return
     */
    public LocalDate calcCertValidDate(LocalDate expireDay) {
        if (ID_CARD_EXPIRE_PERMANENT.equals(expireDay)) {
            return CYBK_PERMANENT;
        }
        return expireDay;
    }

    /**
     * 每一期担保费：对客利率的等额本息还款金额-8.5%的等额本息还款金额；
     *
     * @param credit
     * @return
     */
    public CYBKLoanGuaranteeInfo getGuaranteeAmtAndRate(CYBKLoanGuaranteeInfo guaranteeInfo, Credit credit) {
        //计算 对客利率的等额本息还款金额
        List<RepayPlan> repayPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST,
                LocalDate.now(), credit.getLoanAmt(), credit.getCustomRate(), credit.getPeriods());
        BigDecimal totalAmt = repayPlanList.stream().map(item -> item.getPrincipal().add(item.getInterest())).reduce(BigDecimal.ZERO, BigDecimal::add);
        logger.info("长银直连, 放款申请生成对客还款计划: {}", JsonUtil.toJsonString(repayPlanList));

        List<RepayPlan> repayBankPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST,
                LocalDate.now(), credit.getLoanAmt(), credit.getBankRate(), credit.getPeriods());
        BigDecimal totalBankAmt = repayBankPlanList.stream().map(item -> item.getPrincipal().add(item.getInterest())).reduce(BigDecimal.ZERO, BigDecimal::add);
        logger.info("长银直连, 放款申请生成对资还款计划: {}", JsonUtil.toJsonString(repayBankPlanList));

        BigDecimal guaranteeAmt = totalAmt.subtract(totalBankAmt); //担保费
        guaranteeInfo.setGuarAmt(guaranteeAmt);

        //担保总金额(12期) = 担保费总额 ÷ 实际期数 × 12
        guaranteeAmt = guaranteeAmt.divide(new BigDecimal(credit.getPeriods()), SIX, RoundingMode.HALF_UP).multiply(new BigDecimal(TWELVE));

        //担保费年费利率=担保费总额/申请金额
        BigDecimal guarRate = guaranteeAmt.divide(credit.getLoanAmt(), PROCESSING_POINT_NUM, RoundingMode.HALF_UP); //平均担保费
        guaranteeInfo.setGuarRate(guarRate);

        //担保费计划
        List<CYBKLoanGuaranteePlan> guaranteeList = new ArrayList<>();
        repayPlanList.forEach(item -> {
            RepayPlan bankRepayPlan = repayBankPlanList.stream().filter(
                    bankItem -> bankItem.getCurrentPeriod() == item.getCurrentPeriod()).findFirst().orElseThrow();
            CYBKLoanGuaranteePlan loanGuaranteePlan = new CYBKLoanGuaranteePlan();
            loanGuaranteePlan.setPerdNo(String.valueOf(item.getCurrentPeriod()));
            loanGuaranteePlan.setPerGuarFee(item.getPrincipal().add(item.getInterest())
                    .subtract(bankRepayPlan.getPrincipal()).subtract(bankRepayPlan.getInterest()));
            loanGuaranteePlan.setGuarDate(item.getRepayDate().format(DateTimeFormatter.ISO_LOCAL_DATE));
            guaranteeList.add(loanGuaranteePlan);
        });
        guaranteeInfo.setGuaranteeList(guaranteeList);

        return guaranteeInfo;
    }

    @Override
    protected LoanResultVo bankLoanApply(Loan loan) {

        LoanResultVo loanResultVo = new LoanResultVo();
        Account account = accountRepository.findById(loan.getAccountId()).orElseThrow();
        //授信阶段签章文件数
        List<AgreementType> agrTypes = AgreementType.getAgreement(loan.getChannel(), LoanStage.LOAN, SignatureType.TEMPLATE);
        //签章文件
        List<AgreementSignature> agreementSignatures = fetchAgreementNeedFile(loan);
        if (agreementSignatures.isEmpty()) {
            for (AgreementType fileType : agrTypes) {
                AgreementSignature agreementSignature = generateAgrSignature(loan.getId(), fileType, SignatureType.TEMPLATE);
                agreementSignature.setBankMobilePhone(account.getMobile());
                agreementSignature.setIdentNo(account.getCertNo());
                agreementSignature.setPersonName(account.getName());
                agreementSignature.setTemplateNo(fileType.getTemplateNo()); //文件模板编号
                agreementSignature.setAddress(account.getLivingAddress());
                AgreementSignature saved = agreementSignatureRepository.save(agreementSignature);
                //签章申请监听
                getMqService().signatureApply(saved.getId());
            }

        }
        loanResultVo.setStatus(ProcessStatus.PROCESSING);
        return loanResultVo;
    }

    /**
     * 处理放款申请返回结果
     *
     * @param loan     借据
     * @param resultVo 返回结果
     */
    @Override
    public void bankLoanApplyResult(Loan loan, LoanResultVo resultVo) {
        ProcessStatus status = resultVo.getStatus();
        switch (status) {
            case FAIL -> {
                loan.setLoanStatus(LoanStatus.FAIL);
                loan.setFailMsg(resultVo.getFailMsg());
                getFinLoanService().updateLoan(loan);
            }
            case PROCESSING -> {
                loan.setLoanStatus(LoanStatus.PROCESSING);
                getFinLoanService().updateLoan(loan);

                sendContractUploadMessage(loan.getId());
                //
            }
            default -> {
            }
        }
    }

    @Override
    public void contractUpload(String loanId) {
        Loan loan = getCommonService().findLoanById(loanId);
        //放款阶段签章文件数
        List<AgreementType> agrTypes = AgreementType.getAgreement(loan.getChannel(), LoanStage.LOAN, SignatureType.TEMPLATE);
        List<FileType> fileTypes = agrTypes.stream().map(AgreementType::getFileType).toList();
        List<LoanFile> loanFiles = fetchAllNeedFile(loan.getCreditId(), loanId, fileTypes);
        logger.info("放款文件: {}", JSONObject.toJSONString(loanFiles));
        if (loanFiles.size() != LOAN_STAGE_FILE_NUM) {
            sendContractUploadDelayMessage(loanId);
            logger.info("长银文件未签署完成: {}", JsonUtil.toJsonString(loanFiles));
            return;
        }

        //        List<AccountBankCard> accountBankCardList = accountBankCardRepository.findByCardNo(loan.getLoanCardId());
        //        AccountBankCard accountBankCard = accountBankCardList.get(0);
        // 获取放款阶段需要上传的文件
        List<LoanFile> stageFiles = getCommonService().getLoanFileRepository().findByRelatedId(loan.getId());
        List<LoanFile> creditFiles = getCommonService().getLoanFileRepository().findByCreditIdAndFileType(loan.getCreditId(), FileType.ID_FACE);
        stageFiles.add(creditFiles.get(0));
        AccountBankCard accountBankCard = accountBankCardRepository.findById(loan.getLoanCardId()).orElseThrow();
        List<CYBKImageInfo> imgs = uploadFile(stageFiles, accountBankCard.getCertNo());
        if (!CollectionUtils.isEmpty(imgs)) {
            for (CYBKImageInfo info : imgs) {
                if (ObjectUtils.isEmpty(info.getImageUrl())) {
                    sendContractUploadDelayMessage(loanId);
                    return;
                }
            }
        }
        // 共享扣款协议
        String signSeq = syncBankProtocol(accountBankCard);
        //请求参数构建
        CYBKLoanApplyRequest request = buildApplyRequest(loan, accountBankCard, imgs, signSeq);
        // 放款申请
        CYBKLoanApplyResponse response = requestService.loanApply(request);
        logger.info("长银直连放款申请响应参数: {}", JsonUtil.toJsonString(response));

        if (Objects.nonNull(response) && StringUtil.isNotBlank(response.getLoanSeq())) {
            CYBKCreditFlow creditFlow = cybKCreditFlowRepository.findByCreditId(loan.getCreditId()).orElseThrow();
            creditFlow.setLoanSeq(response.getLoanSeq());
            creditFlow.setLoanNo(response.getLoanNo());
            cybKCreditFlowRepository.save(creditFlow);
            loan.setLoanNo(response.getLoanSeq());
            getFinLoanService().updateLoan(loan);
        }
        //查询结果MQ
        getMqService().submitLoanResultQueryDelay(loan.getId());
    }

    private void sendContractUploadDelayMessage(String businessId) {
        // 监测放款阶段文件
        ContractBizDto contractBizDto = new ContractBizDto();
        contractBizDto.setStage(LoanStage.LOAN);
        contractBizDto.setBusinessId(businessId);
        getMqService().submitContractUploadDelay(JsonUtil.toJsonString(contractBizDto));
    }

    private void sendContractUploadMessage(String businessId) {
        // 监测放款阶段文件
        ContractBizDto contractBizDto = new ContractBizDto();
        contractBizDto.setStage(LoanStage.LOAN);
        contractBizDto.setBusinessId(businessId);
        getMqService().submitContractUpload(JsonUtil.toJsonString(contractBizDto));
    }

    @Override
    protected LoanResultVo bankLoanQuery(Loan loan, LoanQueryVo query) {
        // 放款结果查询
        CYBKLoanQueryRequest request = new CYBKLoanQueryRequest();
        request.setOutLoanSeq(loan.getId());
        request.setLoanSeq(loan.getLoanNo());
        CYBKLoanQueryResponse response = requestService.loanQuery(request);
        logger.info("长银直连放款申请状态查询响应参数:{}", JsonUtil.toJsonString(response));
        LoanResultVo applyResultVo = CYBKLoanConvert.INSTANCE.toVo(response);
        //更新借据信息
        CYBKCreditFlow creditFlow = cybKCreditFlowRepository.findByCreditId(loan.getCreditId()).orElseThrow();
        applyResultVo.setFundingModel(creditFlow.getFundingModel());
        creditFlow.setLoanSeq(response.getLoanSeq());
        creditFlow.setLoanNo(response.getLoanNo());
        cybKCreditFlowRepository.save(creditFlow);

        return applyResultVo;
    }

    @Override
    protected PlanVo getBankPlan(Loan loan) {
        // 查询还款计划
        CYBKRepayPlanQueryRequest request = new CYBKRepayPlanQueryRequest();
        Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();
        CYBKCreditFlow creditFlow = cybKCreditFlowRepository.findByCreditId(credit.getId()).orElseThrow();
        request.setApplCde(credit.getCreditNo());
        request.setLoanSeq(loan.getLoanNo());
        request.setLoanNo(creditFlow.getLoanNo());
        request.setEnqTyp("ALL"); //查所有期
        logger.info("长银直连还款计划查询请求参数:{}", JsonUtil.toJsonString(request));
        CYBKRepayPlanQueryResponse response = requestService.repayPlanQuery(request);
        logger.info("长银直连还款计划查询响应参数:{}", JsonUtil.toJsonString(response));
        return toPlanVo(loan, response);
    }

    private PlanVo toPlanVo(Loan loan, CYBKRepayPlanQueryResponse response) {

        PlanVo planVo = new PlanVo();
        planVo.setChannel(loan.getChannel());
        planVo.setLoanId(loan.getId());
        planVo.setLoanAmt(loan.getLoanAmt());
        planVo.setPeriods(loan.getPeriods());
        // 还款计划映射
        List<PlanItemVo> planItemVoList = CYBKLoanConvert.INSTANCE.toPlanVo(response.getRepaymentPlanList());
        //对资金方不需要担保费
        //fillGuaranteeAmt(loan, planItemVoList); //担保费

        planItemVoList.forEach(p -> {
            p.setLoanId(planVo.getLoanId());
            p.setChannel(planVo.getChannel());
            p.setTotalAmt(p.getPrincipalAmt().add(p.getInterestAmt()).add(p.getGuaranteeAmt()));
            p.setCustRepayStatus(RepayStatus.NORMAL);
            p.setBankRepayStatus(RepayStatus.NORMAL);
        });


        planVo.setPlanItems(planItemVoList);
        return planVo;
    }

    @Override
    public RepayDateResultVo appropriationInterest(RepayDateApplyVo applyVo) {
        return null;
    }

    @Override
    public LoanLprResultVo lpr(Credit credit) {
        CYBKLprQueryRequest lprQueryRequest = new CYBKLprQueryRequest();
        lprQueryRequest.setSignDate(credit.getPassTime().format(DateTimeFormatter.ISO_LOCAL_DATE));
        lprQueryRequest.setPriceRate(credit.getBankRate());
        CYBKLprQueryResponse response = requestService.lprQuery(lprQueryRequest);

        LoanLprResultVo resultVo = new LoanLprResultVo();
        resultVo.setLprRate(response.getLprRate().toPlainString());
        resultVo.setLprDate(response.getLprDate());
        // 资方利率(单位百分比)
        resultVo.setBusinessRate(credit.getBankRate().multiply(BigDecimal.valueOf(PERCENT)).toString());

        return resultVo;
    }

    //查询签署完成协议文件
    private List<LoanFile> fetchAllNeedFile(String creditId, String loanId, List<FileType> fileTypes) {
        return getCommonService().getLoanFileRepository().findByCreditIdAndRelatedIdAndFileTypeList(creditId, loanId, LoanStage.LOAN.name(), fileTypes);
    }

    //放款前协议信息
    private List<AgreementSignature> fetchAgreementNeedFile(Loan loan) {
        List<AgreementType> agrTypes = AgreementType.getAgreement(loan.getChannel(), LoanStage.LOAN, SignatureType.TEMPLATE);
        List<FileType> fileTypes = new ArrayList<>(agrTypes.stream().map(AgreementType::getFileType).toList());
        return agreementSignatureRepository.findByChannelAndLoanStageAndFileTypeList(loan.getId(), BankChannel.CYBK, LoanStage.LOAN, fileTypes);
    }

    //签章信息
    private AgreementSignature generateAgrSignature(String loanId, AgreementType fileType, SignatureType signatureType) {
        AgreementSignature signature = new AgreementSignature();
        signature.setBusinessId(loanId);
        signature.setChannel(BankChannel.CYBK);
        signature.setFileType(fileType.getFileType());
        signature.setSignState(ProcessStatus.INIT);
        signature.setLoanStage(LoanStage.LOAN);
        signature.setSignatureType(signatureType);
        signature.setDynamicOssBucket("");
        signature.setDynamicOssKey("");
        return signature;
    }

    /**
     * 填充融担费用
     *
     * @param loan
     * @return
     */
    public void fillGuaranteeAmt(Loan loan, List<PlanItemVo> planItemVoList) {
        Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();

        //计算 对客利率的等额本息还款金额
        List<RepayPlan> repayPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST,
                LocalDate.now(), loan.getLoanAmt(), credit.getCustomRate(), credit.getPeriods());

        Map<Integer, RepayPlan> repayMap = repayPlanList.stream().collect(Collectors.toMap(RepayPlan::getCurrentPeriod, Function.identity(), (o1, o2) -> o2));

        planItemVoList.forEach(p -> {
            RepayPlan repayPlan = repayMap.get(p.getPeriod());
            p.setGuaranteeAmt(repayPlan.getPrincipal().add(repayPlan.getInterest()).subtract(p.getPrincipalAmt()).subtract(p.getInterestAmt()));
        });
    }

    /**
     * 放款回调处理
     *
     * @param loanId
     * @param loanCallBackResultStatus
     * @return
     */
    public String doLoanResultCallback(String loanId, ProcessStatus loanCallBackResultStatus) {
        try {
            Loan loan = getCommonService().findLoanById(loanId);
            //判断放款记录是否存在
            if (loan == null) {
                getWarningService().warn("回调放款记录不存在，放款loanId：" + loanId);
                return CYBKCallBackCommonResult.fail("放款记录不存在");
            }
            //落库数据终态判断
            if (loan.getLoanStatus().isFinal()) {
                //落库数据终态且与回调状态一致直接返回
                if (loanCallBackResultStatus.name().equals(loan.getLoanStatus().name())) {
                    //终态一致
                    return CYBKCallBackCommonResult.success();
                } else {
                    //终态不一致
                    getWarningService().warn("放款回调状态和已落库终态不一致，借款loanId：" + loanId + "，回调状态："
                            + loanCallBackResultStatus.name() + "，已落库状态：" + loan.getLoanStatus().name());
                    return CYBKCallBackCommonResult.fail("放款回调状态和已落库终态不一致");
                }
            }
            // 放款结果查询
            CYBKLoanQueryRequest request = new CYBKLoanQueryRequest();
            request.setOutLoanSeq(loan.getId());
            request.setLoanSeq(loan.getLoanNo());
            logger.info("长银回调放款申请查询请求:{}", JsonUtil.toJsonString(request));
            CYBKLoanQueryResponse response = requestService.loanQuery(request);
            logger.info("长银回调放款申请查询响应:{}", JsonUtil.toJsonString(response));
            LoanResultVo loanResultVo = CYBKLoanConvert.INSTANCE.toVo(response);

            //判断回调状态与主动查询状态是否一致
            if (!loanCallBackResultStatus.name().equals(loanResultVo.getStatus().name())) {
                //不一致
                getWarningService().warn("放款结果回调状态和主动查询状态不一致，借款loanId：" + loanId + "，回调状态："
                        + loanCallBackResultStatus.name() + "，主动查询状态：" + loanResultVo.getStatus().name());
                return CYBKCallBackCommonResult.fail("放款结果回调状态和主动查询状态不一致");
            }

            //更新借据信息
            CYBKCreditFlow creditFlow = cybKCreditFlowRepository.findByCreditId(loan.getCreditId()).orElseThrow();
            loanResultVo.setFundingModel(creditFlow.getFundingModel());
            creditFlow.setLoanSeq(response.getLoanSeq());
            creditFlow.setLoanNo(response.getLoanNo());
            cybKCreditFlowRepository.save(creditFlow);
            // 放款结果
            loan.setLoanNo(StringUtil.isNotBlank(loanResultVo.getLoanNo()) ? loanResultVo.getLoanNo() : loan.getLoanNo());
            loan.setLoanContractNo(StringUtil.isNotBlank(loanResultVo.getLoanContractNo()) ? loanResultVo.getLoanContractNo() : loan.getLoanContractNo());
            loan.setLoanChannel(loanResultVo.getLoanChannel());
            loan.setLoanStatus(ProcessStatus.SUCCESS.equals(loanResultVo.getStatus()) ? LoanStatus.SUCCESS : LoanStatus.FAIL);
            loan.setFailMsg(loanResultVo.getFailMsg());
            loan.setLoanTime(null != loanResultVo.getLoanTime() ? loanResultVo.getLoanTime() : loan.getLoanTime());
            // 出资模式
            loan.setFundingModel(loanResultVo.getFundingModel());
            if (ProcessStatus.SUCCESS.equals(loanResultVo.getStatus())) {
                // 生成还款计划
                logger.info("生成还款计划, loanId: {}", loanResultVo.getLoanId());
                PlanVo planVo = getBankPlan(loan);

                if (CollectionUtil.isEmpty(planVo.getPlanItems()) || !Objects.equals(loan.getPeriods(), planVo.getPlanItems().size())) {
                    logger.error("还款计划条数与借款期数不一致，loanId: {}，period:{}, loanRepayPlan size:{}", loan.getId(), loan.getPeriods(), planVo.getPlanItems().size());
                    warningService.warn("还款计划条数与借款期数不一致, loanId:" + loan.getId());
                    mqService.submitLoanResultQueryDelay(loan.getId());
                    return CYBKCallBackCommonResult.fail("还款计划条数与借款期数不一致");
                }
                //保存更新到本地还款计划表
                List<LoanReplan> replanList = finLoanService.genRepayPlans(planVo); // save the plan list
            }
            getFinLoanService().updateLoan(loan);
            return CYBKCallBackCommonResult.success();
        } catch (Exception e) {
            logger.error("放款回调异常：" + e);
            return CYBKCallBackCommonResult.fail();
        }
    }

    public String getCreditLevel(String acardScore) {
        if (StringUtil.isNotBlank(acardScore)) {
            int score = Integer.parseInt(acardScore);
            if (score <= oneThousand && score > fourHundred) {
                return "A";
            } else if (score <= fourHundred && score > threeHundred) {
                return "B";
            } else if (score <= threeHundred && score > twoHundred) {
                return "C";
            } else {
                return "D";
            }
        } else {
            return "D";
        }
    }

    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.CYBK == channel;
    }

    @Autowired
    public void setRequestService(CYBKRequestService requestService) {
        this.requestService = requestService;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setCreditRepository(CreditRepository creditRepository) {
        this.creditRepository = creditRepository;
    }

    @Autowired
    public void setCYBKCreditFlowRepository(CYBKCreditFlowRepository cybKCreditFlowRepository) {
        this.cybKCreditFlowRepository = cybKCreditFlowRepository;
    }

    @Autowired
    public void setAccountRepository(AccountRepository accountRepository) {
        this.accountRepository = accountRepository;
    }

    @Autowired
    public void setAccountBankCardRepository(AccountBankCardRepository accountBankCardRepository) {
        this.accountBankCardRepository = accountBankCardRepository;
    }

    @Autowired
    public void setAgreementSignatureRepository(AgreementSignatureRepository agreementSignatureRepository) {
        this.agreementSignatureRepository = agreementSignatureRepository;
    }

    @Autowired

    public void setAccountContactInfoRepository(AccountContactInfoRepository accountContactInfoRepository) {
        this.accountContactInfoRepository = accountContactInfoRepository;
    }

    @Autowired
    public void setLoanReplanRepository(LoanReplanRepository loanReplanRepository) {
        this.loanReplanRepository = loanReplanRepository;
    }

    @Autowired
    public void setCybKConfig(CYBKConfig cybKConfig) {
        this.cybKConfig = cybKConfig;
    }

    @Autowired
    public void setCybkBankRepository(CYBKBankRepository cybkBankRepository) {
        this.cybkBankRepository = cybkBankRepository;
    }

}
