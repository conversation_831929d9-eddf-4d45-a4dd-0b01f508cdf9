package com.jinghang.capital.core.banks.cybk.service;


import com.jinghang.capital.core.banks.BankReccService;
import com.jinghang.capital.core.banks.cybk.recc.CYBKReccHandler;
import com.jinghang.capital.core.banks.cybk.recc.CYBKReccQueryHandler;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.vo.recc.ReccApplyVo;
import com.jinghang.capital.core.vo.recc.ReccDownloadVo;
import com.jinghang.capital.core.vo.recc.ReccResultVo;
import com.jinghang.capital.core.vo.recc.ReccType;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/8/28 15:27
 */
@Service
public class CYBKReccService implements BankReccService, InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(CYBKReccService.class);

    @Autowired
    private List<CYBKReccHandler> reccHandlers;

    private Map<ReccType, CYBKReccHandler> reccHandlerMap;

    @Autowired
    private List<CYBKReccQueryHandler> reccQueryHandlers;

    private Map<ReccType, CYBKReccQueryHandler> reccQueryHandlerMap;


    @Override
    public void process(ReccApplyVo apply) {
        logger.info("开始处理 CYBK 对账. reccDay: [{}], type: [{}]", apply.getReccDay(), apply.getReccType());
        reccHandlerMap.get(apply.getReccType()).process(apply.getReccDay());
    }

    @Override
    public ReccResultVo query(ReccApplyVo apply) {
        logger.info("开始查询 CYBK 对账 reccDay: [{}], type: [{}]", apply.getReccDay(), apply.getReccType());
        return reccQueryHandlerMap.get(apply.getReccType()).query(apply.getReccDay());
    }

    @Override
    public ReccResultVo download(ReccDownloadVo vo) {
        return null;
    }

    @Override
    public boolean isSupport(BankChannel channel) {
        return BankChannel.CYBK == channel;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        reccHandlerMap = reccHandlers.stream().collect(Collectors.toMap(CYBKReccHandler::getReccType, Function.identity()));
        reccQueryHandlerMap = reccQueryHandlers.stream().collect(Collectors.toMap(CYBKReccQueryHandler::getReccType, Function.identity()));
    }
}
