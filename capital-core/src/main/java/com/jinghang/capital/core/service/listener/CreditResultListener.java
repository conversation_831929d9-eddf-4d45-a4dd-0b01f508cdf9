package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.*;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/5/14
 */
@Component
public class CreditResultListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CreditResultListener.class);
    private final int five = 5;
    private final int waitTime = 2;

    public CreditResultListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @Autowired
    private LockService lockService;

    @RabbitListener(queues = RabbitConfig.Queues.CREDIT_QUERY)
    public void listenCreditResult(Message message, Channel channel) {
        String creditId = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("监听授信结果:{}", creditId);

        String redisKey = "fin_creditId_query_" + creditId;
        Locker lock = lockService.getLock(redisKey);

        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(waitTime), Duration.ofSeconds(five));
            if (locked) {
                logger.info("监听授信结果,获取锁成功,key：{}", redisKey);
                // manageService
                manageService.creditQuery(creditId);
            } else {
                logger.info("监听授信结果,获取锁失败,可能其他线程正在处理,creditId: {}", creditId);
            }
        } catch (Exception e) {
            logger.error("credit query error. creditId: {}", creditId, e);
            processException(creditId, message, e, "查询授信结果异常", getMqService()::submitCreditResultQueryDelay);
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
            ackMsg(creditId, message, channel);
        }
    }

}
