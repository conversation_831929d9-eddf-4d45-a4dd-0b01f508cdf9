package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jinghang.cash.modules.manage.vo.req.RightsInfoQueryRequest;
import com.jinghang.cash.modules.manage.vo.res.RightsInfoQueryResponse;
import com.jinghang.cash.pojo.RightsOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * <AUTHOR>
 * @description 针对表【rights_order(权益订单)】的数据库操作Mapper
 * @createDate 2023-11-16 11:45:25
 * @Entity com.jinghang.cash.pojo.RightsOrder
 */
@Mapper
@DS("slave")
public interface RightsOrderMapper extends BaseMapper<RightsOrder> {

    RightsOrder selectRightsOrder(RightsOrder rightsOrder);



    List<RightsInfoQueryResponse> selectRightsInfoQueryRequest(RightsInfoQueryRequest request);

    RightsInfoQueryResponse selectRightsAmountAndRefundAmount(RightsInfoQueryRequest request);
}




