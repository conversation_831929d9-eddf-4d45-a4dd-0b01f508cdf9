package com.jinghang.cash.modules.manage.mapstruct;


import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.capital.api.dto.loan.LoanQueryDto;
import com.jinghang.capital.api.dto.loan.LoanVoucherResultDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * <AUTHOR>
 * @date 2023/12/3
 */
@Mapper
public interface VoucherFileMapstruct {
    VoucherFileMapstruct INSTANCE = Mappers.getMapper(VoucherFileMapstruct.class);

   FileDownloadDto copy(FileDownloadDto fileDownloadDto);
    FileDownloadResultDto copy(FileDownloadResultDto fileDownloadDto);
    LoanQueryDto copy(LoanQueryDto fileDownloadDto);
    LoanVoucherResultDto copy(LoanVoucherResultDto fileDownloadDto);
}
