/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghang.cash.api.dto.CapitalConfigDto;
import com.jinghang.cash.modules.project.domain.CapitalConfig;
import com.jinghang.cash.modules.project.domain.dto.CapitalConfigQueryCriteria;
import com.jinghang.cash.utils.PageResult;

import java.util.List;
import java.util.Map;

/**
* @description 服务接口
* <AUTHOR>
* @date 2025-08-22
**/
public interface CapitalConfigService extends IService<CapitalConfig> {



    /**
     * 创建
     * @param resources /
     */
    CapitalConfig selectByid(String id);


    List<Map<String,String>> queryByChannel(String channel);



    /**
     * 查询数据分页
     * @param criteria 条件
     * @param page 分页参数
     * @return PageResult
     */
    PageResult<CapitalConfig> selectAll(CapitalConfigQueryCriteria criteria, Page<Object> page);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<CapitalConfigDto>
    */
//    List<CapitalConfigs> queryAll(CapitalConfigQueryCriteria criteria);

    /**
    * 创建
    * @param resources /
    */
    Map<String,String> create(CapitalConfigDto resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(CapitalConfigDto resources);


    void enable(CapitalConfigDto resources);


    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(List<String> ids);

}