/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.api.dto.CapitalConfigDto;
import com.jinghang.cash.api.enums.AbleStatusExt;
import com.jinghang.cash.modules.project.domain.CapitalConfig;
import com.jinghang.cash.modules.project.domain.dto.CapitalConfigQueryCriteria;
import com.jinghang.cash.modules.project.mapper.CapitalConfigMapper;
import com.jinghang.cash.modules.project.service.CapitalConfigService;
import com.jinghang.cash.utils.PageResult;
import com.jinghang.cash.utils.PageUtil;
import com.jinghang.cash.utils.SecurityUtils;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
* @description 服务实现
* <AUTHOR>
* @date 2025-08-22
**/
@Service
@RequiredArgsConstructor
public class CapitalConfigServiceImpl extends ServiceImpl<CapitalConfigMapper, CapitalConfig> implements CapitalConfigService {


    private final CapitalConfigMapper capitalConfigMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CapitalConfig selectByid(String id) {
        return capitalConfigMapper.selectById(id);
    }


    @Override
    public List<Map<String,String>> queryByChannel(String channel) {
        List<Map<String,String>> maps = new ArrayList<>();
        LambdaQueryWrapper<CapitalConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(StringUtil.isNotBlank(channel)){
            lambdaQueryWrapper.eq(CapitalConfig::getBankChannel, channel);
        }
        lambdaQueryWrapper.eq(CapitalConfig::getEnabled,AbleStatusExt.ENABLE.name());
        List<CapitalConfig> capitalConfigs = capitalConfigMapper.selectList(lambdaQueryWrapper);
        if(!CollectionUtil.isEmpty(capitalConfigs)){
            List<Map<String, String>> result = capitalConfigs.stream()
                    .map(x -> {
                        Map<String, String> map = new HashMap<>();
                        map.put("id", x.getId());
                        map.put("name", x.getCapitalName());
                        return map;
                    })
                    .collect(Collectors.toList());
            maps.addAll(result);
        }
        return maps;
    }

    @Override
    public PageResult<CapitalConfig> selectAll(CapitalConfigQueryCriteria criteria, Page<Object> page){
        //创建分页对象（pageNum：页码，pageSize：每页条数）
        Page<CapitalConfig> capitalpage = new Page<>(criteria.getPageNum(),criteria.getPageSize());
        QueryWrapper<CapitalConfig> queryWrapper = new QueryWrapper<>();
        if (StringUtil.isNotBlank(criteria.getBankChannel())){
            queryWrapper.eq("bank_channel",criteria.getBankChannel());
        }
        if (StringUtil.isNotBlank(criteria.getCapitalName())){
            queryWrapper.like("capital_name",criteria.getCapitalName());
        }
        if (StringUtil.isNotBlank(criteria.getEnabled())){
            queryWrapper.eq("enabled",criteria.getEnabled());
        }
        Page<CapitalConfig> capitalConfigPage = capitalConfigMapper.selectPage(capitalpage, queryWrapper);
        return PageUtil.toPage(capitalConfigPage.getRecords(),capitalConfigPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String,String> create(CapitalConfigDto resources) {
        CapitalConfig config = new CapitalConfig();
        BeanUtil.copyProperties(resources,config);
        String id = "C" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
        config.setId(id);
        config.setCreatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        config.setCreatedTime(LocalDateTime.now());
        config.setEnabled(AbleStatusExt.INIT.name());
        capitalConfigMapper.insert(config);
        Map<String,String> map = new HashMap<>();
        map.put("id",id);
        map.put("bankChannel",resources.getBankChannel());
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CapitalConfigDto resources) {
        CapitalConfig capitalConfig = getById(resources.getId());
        capitalConfig.copy(resources);
        capitalConfig.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        capitalConfig.setUpdatedTime(LocalDateTime.now());
        capitalConfigMapper.updateById(capitalConfig);
    }


    @Override
    public void enable(CapitalConfigDto resources) {
        CapitalConfig capitalConfig = getById(resources.getId());
        capitalConfig.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        capitalConfig.setUpdatedTime(LocalDateTime.now());
        capitalConfig.setEnabled(resources.getEnabled());
        capitalConfigMapper.updateById(capitalConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<String> ids) {
        capitalConfigMapper.deleteBatchIds(ids);
    }

}