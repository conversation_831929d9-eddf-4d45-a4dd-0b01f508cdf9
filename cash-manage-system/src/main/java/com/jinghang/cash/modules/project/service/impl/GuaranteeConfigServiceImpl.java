/*
 *  Copyright 2019-2025 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.jinghang.cash.modules.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.api.dto.GuaranteeConfigDto;
import com.jinghang.cash.api.enums.AbleStatusExt;
import com.jinghang.cash.modules.project.domain.GuaranteeConfig;
import com.jinghang.cash.modules.project.domain.dto.GuaranteeConfigQueryCriteria;
import com.jinghang.cash.modules.project.mapper.GuaranteeConfigMapper;
import com.jinghang.cash.modules.project.service.GuaranteeConfigService;
import com.jinghang.cash.utils.PageResult;
import com.jinghang.cash.utils.PageUtil;
import com.jinghang.cash.utils.SecurityUtils;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 服务实现
 * @date 2025-08-22
 **/
@Service
@RequiredArgsConstructor
public class GuaranteeConfigServiceImpl extends ServiceImpl<GuaranteeConfigMapper, GuaranteeConfig> implements GuaranteeConfigService {

    private final GuaranteeConfigMapper guaranteeConfigMapper;

    @Override
    public GuaranteeConfig selectByid(String id) {
        GuaranteeConfig guaranteeConfig = guaranteeConfigMapper.selectById(id);
        return guaranteeConfig;
    }

    @Override
    public List<Map<String,String>> queryByChannel(String code) {
        List<Map<String,String>> maps = new ArrayList<>();
        LambdaQueryWrapper<GuaranteeConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(StringUtil.isNotBlank(code)){
            lambdaQueryWrapper.eq(GuaranteeConfig::getGuaranteeCode, code);
        }
        lambdaQueryWrapper.eq(GuaranteeConfig::getEnabled,AbleStatusExt.ENABLE.name());
        List<GuaranteeConfig> guaranteeConfigs = guaranteeConfigMapper.selectList(lambdaQueryWrapper);
        if(!CollectionUtil.isEmpty(guaranteeConfigs)){
            List<Map<String, String>> result = guaranteeConfigs.stream()
                    .map(x -> {
                        Map<String, String> map = new HashMap<>();
                        map.put("id", x.getId());
                        map.put("name", x.getGuaranteeName());
                        return map;
                    })
                    .collect(Collectors.toList());
            maps.addAll(result);
        }
        return maps;
    }

    @Override
    public PageResult<GuaranteeConfig> selectAll(GuaranteeConfigQueryCriteria criteria, Page<Object> page) {
        //创建分页对象（pageNum：页码，pageSize：每页条数）
        Page<GuaranteeConfig> capitalpage = new Page<>(criteria.getPageNum(), criteria.getPageSize());
        QueryWrapper<GuaranteeConfig> queryWrapper = new QueryWrapper<>();
        if (criteria.getGuaranteeCode() != null && !criteria.getGuaranteeCode().isEmpty()) {
            queryWrapper.eq("guarantee_code", criteria.getGuaranteeCode());
        }
        if (criteria.getGuaranteeName() != null && !criteria.getGuaranteeName().isEmpty()) {
            queryWrapper.like("guarantee_name", criteria.getGuaranteeName());
        }
        if (criteria.getEnabled() != null && !criteria.getEnabled().isEmpty()) {
            queryWrapper.eq("enabled", criteria.getEnabled());
        }
        Page<GuaranteeConfig> guaranteeConfigPage = guaranteeConfigMapper.selectPage(capitalpage, queryWrapper);
        return PageUtil.toPage(guaranteeConfigPage.getRecords(),guaranteeConfigPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String,String> create(GuaranteeConfigDto resources) {
        GuaranteeConfig config = new GuaranteeConfig();
        BeanUtil.copyProperties(resources,config);
        String id = "G" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
        config.setId(id);
        config.setCreatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        config.setCreatedTime(LocalDateTime.now());
        config.setEnabled(AbleStatusExt.INIT.name());
        guaranteeConfigMapper.insert(config);
        Map<String,String> map = new HashMap<>();
        map.put("id",id);
        map.put("guaranteeCode",resources.getGuaranteeCode());
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(GuaranteeConfigDto resources) {
        GuaranteeConfig guaranteeConfig = getById(resources.getId());
        guaranteeConfig.copy(resources);
        guaranteeConfig.setUpdatedTime(LocalDateTime.now());
        guaranteeConfig.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        guaranteeConfigMapper.updateById(guaranteeConfig);
    }

    @Override
    public void enable(GuaranteeConfigDto resources) {
        GuaranteeConfig guaranteeConfig = getById(resources.getId());
        guaranteeConfig.setUpdatedTime(LocalDateTime.now());
        guaranteeConfig.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        guaranteeConfig.setEnabled(resources.getEnabled());
        guaranteeConfigMapper.updateById(guaranteeConfig);
    }
}
