# 独立LDAP服务企业微信集成方案

## 📋 方案概述

搭建一个独立的LDAP服务，作为企业微信和Metabase之间的桥梁，实现企业微信用户信息自动同步到LDAP，Metabase通过LDAP认证实现统一身份管理。

## 🏗️ 整体架构

```
企业微信 → 独立LDAP服务 → Metabase
    ↓           ↓            ↓
  用户信息   LDAP目录服务   LDAP认证
  组织架构   定时同步       权限管理
```

## 🎯 方案选择

### 方案一：OpenLDAP + 自研同步服务（推荐）

#### 技术栈
- **OpenLDAP**: 开源LDAP目录服务
- **Spring Boot**: 企业微信同步服务
- **MySQL**: 同步状态存储
- **Docker**: 容器化部署

#### 优势
- 完全开源免费
- 功能强大，支持复杂组织架构
- 社区活跃，文档丰富
- 可定制性强

### 方案二：FreeIPA（企业级选择）

#### 技术栈
- **FreeIPA**: 集成身份管理解决方案
- **Python脚本**: 企业微信同步
- **PostgreSQL**: 内置数据存储

#### 优势
- 企业级身份管理
- 内置Web管理界面
- 集成DNS、证书管理
- 更易管理

### 方案三：Apache Directory Studio + ApacheDS

#### 技术栈
- **ApacheDS**: Java实现的LDAP服务器
- **Apache Directory Studio**: 图形化管理工具
- **Java**: 同步服务开发

#### 优势
- 纯Java实现，跨平台
- 图形化管理界面友好
- 轻量级部署

## 🚀 推荐实施方案：OpenLDAP + Spring Boot

### 第一步：环境准备

#### 1.1 服务器要求
```bash
# 最低配置
CPU: 2核
内存: 4GB
硬盘: 50GB
操作系统: Ubuntu 20.04 LTS / CentOS 8
```

#### 1.2 Docker环境安装
```bash
# Ubuntu
sudo apt update
sudo apt install docker.io docker-compose

# CentOS
sudo yum install docker docker-compose

# 启动Docker
sudo systemctl start docker
sudo systemctl enable docker
```

### 第二步：OpenLDAP部署

#### 2.1 Docker Compose配置
创建 `docker-compose.yml`：
```yaml
version: '3.8'

services:
  openldap:
    image: osixia/openldap:1.5.0
    container_name: openldap
    environment:
      LDAP_LOG_LEVEL: "256"
      LDAP_ORGANISATION: "Company"
      LDAP_DOMAIN: "company.com"
      LDAP_BASE_DN: "dc=company,dc=com"
      LDAP_ADMIN_PASSWORD: "admin_password"
      LDAP_CONFIG_PASSWORD: "config_password"
      LDAP_READONLY_USER: "false"
      LDAP_RFC2307BIS_SCHEMA: "false"
      LDAP_BACKEND: "mdb"
      LDAP_TLS: "true"
      LDAP_TLS_CRT_FILENAME: "ldap.crt"
      LDAP_TLS_KEY_FILENAME: "ldap.key"
      LDAP_TLS_DH_PARAM_FILENAME: "dhparam.pem"
      LDAP_TLS_CA_CRT_FILENAME: "ca.crt"
      LDAP_TLS_ENFORCE: "false"
      LDAP_TLS_CIPHER_SUITE: "SECURE256:-VERS-SSL3.0"
      LDAP_TLS_VERIFY_CLIENT: "demand"
      LDAP_REPLICATION: "false"
      KEEP_EXISTING_CONFIG: "false"
      LDAP_REMOVE_CONFIG_AFTER_SETUP: "true"
      LDAP_SSL_HELPER_PREFIX: "ldap"
    tty: true
    stdin_open: true
    volumes:
      - /var/lib/ldap
      - /etc/ldap/slapd.d
      - /container/service/slapd/assets/certs/
    ports:
      - "389:389"
      - "636:636"
    hostname: openldap-host
    networks:
      - ldap-network

  phpldapadmin:
    image: osixia/phpldapadmin:latest
    container_name: phpldapadmin
    environment:
      PHPLDAPADMIN_LDAP_HOSTS: "openldap"
      PHPLDAPADMIN_HTTPS: "false"
    ports:
      - "8080:80"
    depends_on:
      - openldap
    networks:
      - ldap-network

  mysql:
    image: mysql:8.0
    container_name: ldap-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: wechat_ldap_sync
      MYSQL_USER: ldap_user
      MYSQL_PASSWORD: ldap_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - ldap-network

  sync-service:
    build: ./sync-service
    container_name: wechat-ldap-sync
    environment:
      SPRING_PROFILES_ACTIVE: docker
      LDAP_URL: ldap://openldap:389
      LDAP_BASE_DN: dc=company,dc=com
      LDAP_ADMIN_DN: cn=admin,dc=company,dc=com
      LDAP_ADMIN_PASSWORD: admin_password
      MYSQL_URL: ****************************************
      MYSQL_USERNAME: ldap_user
      MYSQL_PASSWORD: ldap_password
      WECHAT_CORP_ID: your_corp_id
      WECHAT_CORP_SECRET: your_corp_secret
    depends_on:
      - openldap
      - mysql
    networks:
      - ldap-network
    ports:
      - "8081:8080"

volumes:
  mysql_data:

networks:
  ldap-network:
    driver: bridge
```

#### 2.2 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs openldap
```

#### 2.3 验证LDAP服务
```bash
# 测试LDAP连接
ldapsearch -x -H ldap://localhost:389 -b "dc=company,dc=com" -D "cn=admin,dc=company,dc=com" -W

# 访问Web管理界面
# http://localhost:8080
# 登录DN: cn=admin,dc=company,dc=com
# 密码: admin_password
```

### 第三步：企业微信同步服务开发

#### 3.1 项目结构
```
wechat-ldap-sync/
├── src/main/java/com/company/ldap/
│   ├── WechatLdapSyncApplication.java
│   ├── config/
│   │   ├── LdapConfig.java
│   │   ├── WechatConfig.java
│   │   └── ScheduleConfig.java
│   ├── service/
│   │   ├── WechatApiService.java
│   │   ├── LdapSyncService.java
│   │   └── UserMappingService.java
│   ├── entity/
│   │   ├── WechatUser.java
│   │   ├── LdapUser.java
│   │   └── SyncRecord.java
│   ├── repository/
│   │   └── SyncRecordRepository.java
│   └── controller/
│       └── SyncController.java
├── src/main/resources/
│   ├── application.yml
│   ├── application-docker.yml
│   └── ldap-schema.ldif
├── Dockerfile
└── pom.xml
```

#### 3.2 核心配置文件

**application.yml**:
```yaml
server:
  port: 8080

spring:
  application:
    name: wechat-ldap-sync
  
  datasource:
    url: ${MYSQL_URL:********************************************}
    username: ${MYSQL_USERNAME:ldap_user}
    password: ${MYSQL_PASSWORD:ldap_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    database-platform: org.hibernate.dialect.MySQL8Dialect

# LDAP配置
ldap:
  url: ${LDAP_URL:ldap://localhost:389}
  base-dn: ${LDAP_BASE_DN:dc=company,dc=com}
  admin-dn: ${LDAP_ADMIN_DN:cn=admin,dc=company,dc=com}
  admin-password: ${LDAP_ADMIN_PASSWORD:admin_password}
  user-base: ou=people,dc=company,dc=com
  group-base: ou=groups,dc=company,dc=com

# 企业微信配置
wechat:
  corp-id: ${WECHAT_CORP_ID:your_corp_id}
  corp-secret: ${WECHAT_CORP_SECRET:your_corp_secret}
  api-base-url: https://qyapi.weixin.qq.com

# 同步配置
sync:
  enabled: true
  interval: 3600000  # 1小时
  batch-size: 100

logging:
  level:
    com.company.ldap: DEBUG
    org.springframework.ldap: DEBUG
```

**pom.xml**:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.0</version>
        <relativePath/>
    </parent>
    
    <groupId>com.company</groupId>
    <artifactId>wechat-ldap-sync</artifactId>
    <version>1.0.0</version>
    <name>wechat-ldap-sync</name>
    <description>企业微信LDAP同步服务</description>
    
    <properties>
        <java.version>8</java.version>
    </properties>
    
    <dependencies>
        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        
        <!-- Spring LDAP -->
        <dependency>
            <groupId>org.springframework.ldap</groupId>
            <artifactId>spring-ldap-core</artifactId>
        </dependency>
        
        <!-- MySQL Driver -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        
        <!-- HTTP Client -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        
        <!-- JSON Processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        
        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

#### 3.3 Dockerfile
```dockerfile
FROM openjdk:8-jdk-alpine

VOLUME /tmp

COPY target/wechat-ldap-sync-1.0.0.jar app.jar

ENTRYPOINT ["java","-jar","/app.jar"]

EXPOSE 8080
```

### 第四步：LDAP目录结构初始化

#### 4.1 创建基础目录结构
创建 `init-ldap.ldif`：
```ldif
# 创建组织单元
dn: ou=people,dc=company,dc=com
objectClass: organizationalUnit
ou: people

dn: ou=groups,dc=company,dc=com
objectClass: organizationalUnit
ou: groups

# 创建默认用户组
dn: cn=metabase_admin,ou=groups,dc=company,dc=com
objectClass: groupOfNames
cn: metabase_admin
description: Metabase管理员组
member: cn=admin,dc=company,dc=com

dn: cn=metabase_user,ou=groups,dc=company,dc=com
objectClass: groupOfNames
cn: metabase_user
description: Metabase普通用户组
member: cn=admin,dc=company,dc=com

# 创建部门组织单元
dn: ou=departments,dc=company,dc=com
objectClass: organizationalUnit
ou: departments

# 示例部门
dn: ou=tech,ou=departments,dc=company,dc=com
objectClass: organizationalUnit
ou: tech
description: 技术部

dn: ou=product,ou=departments,dc=company,dc=com
objectClass: organizationalUnit
ou: product
description: 产品部

dn: ou=operation,ou=departments,dc=company,dc=com
objectClass: organizationalUnit
ou: operation
description: 运营部
```

#### 4.2 导入初始化数据
```bash
# 导入LDIF文件
ldapadd -x -D "cn=admin,dc=company,dc=com" -W -f init-ldap.ldif

# 验证导入结果
ldapsearch -x -H ldap://localhost:389 -b "dc=company,dc=com" -D "cn=admin,dc=company,dc=com" -W
```

### 第五步：Metabase LDAP配置

#### 5.1 Metabase LDAP设置
```bash
# LDAP Host: your-ldap-server-ip
# LDAP Port: 389
# LDAP Security: None
# LDAP Bind DN: cn=admin,dc=company,dc=com
# LDAP Bind Password: admin_password

# User Base: ou=people,dc=company,dc=com
# User Filter: (uid={login})
# Email Attribute: mail
# First Name Attribute: givenName
# Last Name Attribute: sn

# Group Base: ou=groups,dc=company,dc=com
# Group Filter: (member={dn})
```

### 第六步：部署和测试

#### 6.1 构建和部署
```bash
# 构建同步服务
cd sync-service
mvn clean package

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

#### 6.2 测试同步功能
```bash
# 手动触发同步
curl -X POST http://localhost:8081/api/sync/manual

# 查看同步状态
curl http://localhost:8081/api/sync/status

# 查看LDAP用户
ldapsearch -x -H ldap://localhost:389 -b "ou=people,dc=company,dc=com" -D "cn=admin,dc=company,dc=com" -W
```

## 📊 管理和监控

### Web管理界面
- **phpLDAPadmin**: http://localhost:8080
- **同步服务API**: http://localhost:8081

### 监控指标
- 同步成功/失败数量
- LDAP服务状态
- 用户登录统计

## 🔧 维护说明

### 日常维护
1. 定期备份LDAP数据
2. 监控同步服务日志
3. 检查企业微信API调用限制

### 故障排查
1. 检查Docker容器状态
2. 查看同步服务日志
3. 验证LDAP连接

---

*这个方案提供了一个完全独立的LDAP服务，可以作为企业微信和Metabase之间的桥梁，实现统一身份管理。*
