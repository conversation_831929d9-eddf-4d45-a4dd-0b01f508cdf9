package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.AuditState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 风控调额记录表
 */
@Entity
@Table(name = "risk_revolving_record")
public class RiskRevolvingRecord extends BaseEntity {

    /**
     * 用户
     */
    private String userId;

    /**
     * 身份证
     */
    private String certNo;

    /**
     * 审批额度
     */
    private BigDecimal approveAmount;

    /**
     * 审批结果code
     */
    private String approveResultCode;

    /**
     * 审批结果
     */
    @Enumerated(EnumType.STRING)
    private AuditState approveResult;

    /**
     * 通过时间
     */
    private LocalDateTime passTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public BigDecimal getApproveAmount() {
        return approveAmount;
    }

    public void setApproveAmount(BigDecimal approveAmount) {
        this.approveAmount = approveAmount;
    }

    public String getApproveResultCode() {
        return approveResultCode;
    }

    public void setApproveResultCode(String approveResultCode) {
        this.approveResultCode = approveResultCode;
    }

    public AuditState getApproveResult() {
        return approveResult;
    }

    public void setApproveResult(AuditState approveResult) {
        this.approveResult = approveResult;
    }

    public LocalDateTime getPassTime() {
        return passTime;
    }

    public void setPassTime(LocalDateTime passTime) {
        this.passTime = passTime;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    @Override
    protected String prefix() {
        return "RRR";
    }
}


