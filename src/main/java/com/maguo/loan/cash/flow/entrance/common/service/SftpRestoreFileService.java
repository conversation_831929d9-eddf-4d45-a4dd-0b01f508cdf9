package com.maguo.loan.cash.flow.entrance.common.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSSException;
import com.jinghang.common.util.CollectionUtil;
import com.maguo.loan.cash.flow.entity.*;
import com.maguo.loan.cash.flow.entrance.common.dto.request.SftpRestoreFileReqDTO;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.job.agreement.LoanAgreementJob;
import com.maguo.loan.cash.flow.job.ppd.PPDSignJob;
import com.maguo.loan.cash.flow.repository.*;
import com.maguo.loan.cash.flow.service.UserFileService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * sftp 文件恢复服务
 *
 * <AUTHOR>
 */
@Service
public class SftpRestoreFileService {

    private static final Logger logger = LoggerFactory.getLogger(SftpRestoreFileService.class);

    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private UserFileService userFileService;
    @Autowired
    private UserFileRepository userFileRepository;
    @Autowired
    private PPDSignJob ppdSignJob;
    @Autowired
    private LoanAgreementJob loanAgreementJob;
    @Autowired
    private AgreementSignatureRecordRepository agreementSignatureRecordRepository;
    @Autowired
    private SftpRestoreRecordRepository sftpRestoreRecordRepository;

    private static final List<FileType> PPD_AGREEMENT_FILE_TYPES = Arrays.asList(
            FileType.LOAN_CONTRACT,
            FileType.ENTRUSTED_DEDUCTION_LETTER,
            FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER,
            FileType.SYNTHESIS_AUTHORIZATION,
            FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE,
            FileType.LETTER_OF_COMMITMENT,
            FileType.CONSULTING_SERVICE_CONTRACT,
            FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER,
            FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE,
            FileType.PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT,
            FileType.ARBITRATION_AGREEMENT);

    private static final List<FileType> CREDIT_FILE_TYPES = Arrays.asList(
            FileType.LETTER_OF_COMMITMENT,
            FileType.CONSULTING_SERVICE_CONTRACT,
            FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE,
            FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER,
            FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE
    );

    private static final List<FileType> LOAN_FILE_TYPES = Arrays.asList(
            FileType.LOAN_CONTRACT,
            FileType.ENTRUSTED_DEDUCTION_LETTER,
            FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER,
            FileType.SYNTHESIS_AUTHORIZATION,
            FileType.ARBITRATION_AGREEMENT,
            FileType.PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT
    );

    private static final String FILE_NOT_EXISTS = "文件记录不存在";

    public String restoreFile(SftpRestoreFileReqDTO request) {
        logger.info("SftpRestoreFileService.restoreFile start, params:{}", JSON.toJSONString(request));
        String sftpPath = "";
        try {
            String outerOrderId = request.getOuterOrderId();
            FileType fileType = FileType.valueOf(request.getFileType());
            List<SftpRestoreRecord> byOuterOrderIdAndApplyTimeAfter = sftpRestoreRecordRepository.findByOuterOrderIdAndFileTypeAndApplyTimeAfterOrderByCreatedTimeDesc(outerOrderId, fileType, LocalDateTime.now().minusDays(7));
            if (CollectionUtil.isNotEmpty(byOuterOrderIdAndApplyTimeAfter)) {
                return byOuterOrderIdAndApplyTimeAfter.get(0).getSftpPath();
            }
            Order order = orderRepository.findByOuterOrderId(outerOrderId);
            if (ObjectUtils.isEmpty(order)) {
                return "该订单不存在！";
            }
            FlowChannel flowChannel = order.getFlowChannel();
            Loan loan = loanRepository.findByOrderId(order.getId());
            if (ObjectUtils.isEmpty(loan)) {
                return "该订单不存在！";
            }
            String userId = order.getUserId();
            SftpRestoreRecord sftpRestoreRecord = new SftpRestoreRecord();
            sftpRestoreRecord.setFileType(fileType);
            sftpRestoreRecord.setFlowChannel(flowChannel);
            sftpRestoreRecord.setApplyTime(LocalDateTime.now());
            sftpRestoreRecord.setOuterOrderId(outerOrderId);
            if (FlowChannel.PPCJDL.equals(flowChannel)) {
                //恢复合同-拍拍
                if (PPD_AGREEMENT_FILE_TYPES.contains(fileType)) {
                    UserFile userFile;
                    if (CREDIT_FILE_TYPES.contains(fileType)) {
                        Optional<AgreementSignatureRecord> recordByOrderNoAndFileType = agreementSignatureRecordRepository.findLatestAgreementSignatureRecordByOrderNoAndFileType(outerOrderId, fileType.name());
                        if (recordByOrderNoAndFileType.isEmpty()) {
                            return FILE_NOT_EXISTS;
                        }
                        sftpPath = ppdSignJob.uploadPaiPaiFlowAgreementFileToSftp(loan,  recordByOrderNoAndFileType.get().getCommonOssUrl(), recordByOrderNoAndFileType.get().getFileType());
                    }
                    if (LOAN_FILE_TYPES.contains(fileType)){
                        userFile = userFileRepository.findTopByLoanNoAndFileTypeOrderByCreatedTimeDesc(loan.getId(), fileType);
                        if (ObjectUtils.isEmpty(userFile)) {
                            return FILE_NOT_EXISTS;
                        }
                        sftpPath = ppdSignJob.uploadPaiPaiUserFileToSftp(loan, userFile);
                    }
                }
            }
            if (FlowChannel.LVXIN.equals(flowChannel)) {
                //恢复合同-绿信
                if (CREDIT_FILE_TYPES.contains(fileType)) {
                    Optional<AgreementSignatureRecord> recordByOrderNoAndFileType = agreementSignatureRecordRepository.findLatestAgreementSignatureRecordByOrderNoAndFileType(outerOrderId, fileType.name());
                    if (recordByOrderNoAndFileType.isEmpty()) {
                        return FILE_NOT_EXISTS;
                    }
                    sftpPath = loanAgreementJob.uploadFlowAgreementFileToSftp(loan, recordByOrderNoAndFileType.get().getCommonOssUrl(), recordByOrderNoAndFileType.get().getFileType(), order);
                }
                if (LOAN_FILE_TYPES.contains(fileType)) {
                    UserFile userFile = userFileRepository.findTopByLoanNoAndFileTypeOrderByCreatedTimeDesc(loan.getId(), fileType);
                    if (ObjectUtils.isEmpty(userFile)) {
                        return FILE_NOT_EXISTS;
                    }
                    sftpPath = loanAgreementJob.uploadCoreAgreementFileToSftp(loan, userFile, order);
                }
            }
            //恢复结清证明
            if (fileType.equals(FileType.CREDIT_SETTLE_VOUCHER_FILE)) {
                if (!order.getOrderState().equals(OrderState.CLEAR)) {
                    return "该笔订单尚未结清！";
                }
                UserFile voucherFile = userFileRepository.findTopByLoanNoAndFileTypeOrderByCreatedTimeDesc(loan.getId(), fileType);
                if (ObjectUtils.isEmpty(voucherFile)) {
                    return FILE_NOT_EXISTS;
                }
                sftpPath = userFileService.uploadVoucherFileToSftp(voucherFile.getOssKey(), voucherFile.getOssBucket(), loan, voucherFile.getFileType(), order);
            }
            sftpRestoreRecord.setSftpPath(sftpPath);
            sftpRestoreRecordRepository.save(sftpRestoreRecord);
        } catch (IllegalArgumentException e) {
            logger.error("sftp恢复文件失败", e);
            return "文件类型不正确";
        }catch (OSSException e){
            logger.error("sftp恢复文件失败", e);
            return "OSS文件不存在";
        }catch (Exception e) {
            logger.error("sftp恢复文件失败", e);
            return "error";
        }
        logger.info("SftpRestoreFileService.restoreFile end.");
        return sftpPath;
    }
}
