package com.maguo.loan.cash.flow.entrance.common.strategy.fql;

import com.maguo.loan.cash.flow.entrance.common.strategy.ProductCodeStrategy;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.service.CacheService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class FqlFlowStrategy implements ProductCodeStrategy {

    @Autowired
    private CacheService cacheService;

    public static final String MAPPING_KEY_PREFIX = "project_mappings:code:";

    @Override
    public boolean supports(String source, Map<String, String> params) {
        return "FLOW".equalsIgnoreCase(source) &&
            FlowChannel.FQLQY001.name().equalsIgnoreCase(params.get("flowSource"));
    }

    @Override
    public String buildProductCode(Map<String, String> params) {
        String partnerCode = params.get("partnerCode");
        if (StringUtils.isEmpty(partnerCode)) {
            throw new IllegalArgumentException("分期乐流量映射必须包含 'partnerCode' 参数");
        }
        String redisKey = MAPPING_KEY_PREFIX + partnerCode;
        return cacheService.get(redisKey).toString();
    }
}
