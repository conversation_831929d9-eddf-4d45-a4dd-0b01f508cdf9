package com.maguo.loan.cash.flow.entrance.lvxin.controller;


import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.entity.CollisionRecord;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.user.QuotaQueryRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.user.QuotaQueryResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.user.UploadRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.user.UploadResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.user.UserCheckRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.user.UserCheckResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.service.LvxinService;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.service.RiskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("lvxin")
public class LvxinUserController {

    @Autowired
    private RiskService riskService;
    @Autowired
    private LvxinService lvxinService;


    /**
     * 绿信-长银撞库
     */
    @PostMapping({"/api/partner/v1/access","/api/partner/v2/access"})
    public LvxinResponse checkUser(@RequestBody UserCheckRequest applyRequest) {
        UserCheckResponse response = new UserCheckResponse();
        CollisionRecord collisionRecord = riskService.userCheck(applyRequest, FlowChannel.LVXIN);
        if (collisionRecord.getState().equals(AuditState.PASS)) {
            //通过
            response.setResult(1);
            response.setReason(collisionRecord.getFailReason());
        } else {
            //拒绝
            response.setResult(2);
            response.setReason(collisionRecord.getFailReason());
            if(StringUtil.isEmpty(collisionRecord.getFailReason())){
                response.setReason("准入拒绝");
            }
        }
        return LvxinResponse.success(response);
    }

    /**
     * 绿信-湖消内部撞库
     */
    @PostMapping({"/api/partner/v1/front-access", "/api/partner/v2/front-access"})
    public LvxinResponse internalCheckUser(@RequestBody UserCheckRequest applyRequest) {
        UserCheckResponse response = new UserCheckResponse();
        CollisionRecord collisionRecord = riskService.internalUserCheck(applyRequest, FlowChannel.LVXIN);
        if (collisionRecord.getState().equals(AuditState.PASS)) {
            //通过
            response.setResult(1);
            response.setReason(collisionRecord.getFailReason());
        } else {
            //拒绝
            response.setResult(2);
            response.setReason(collisionRecord.getFailReason());
            if(StringUtil.isEmpty(collisionRecord.getFailReason())){
                response.setReason("准入拒绝");
            }
        }
        return LvxinResponse.success(response);
    }

    /**
     * 查询用户额度
     */
    @PostMapping({"/api/partner/v1/queryQuota","/api/partner/v2/queryQuota"})
    public LvxinResponse accountInfoQuery(@RequestBody QuotaQueryRequest quotaQueryRequest) {
        QuotaQueryResponse quotaQueryResponse = lvxinService.queryQuota(quotaQueryRequest);
        return LvxinResponse.success(quotaQueryResponse);
    }

    /**
     * 文件影像上传接口
     */
    @PostMapping({"/api/partner/v1/upload","/api/partner/v2/upload"})
    public LvxinResponse upload(@RequestBody UploadRequest quotaQueryRequest) {
        UploadResponse quotaQueryResponse = lvxinService.upload(quotaQueryRequest);
        return LvxinResponse.success(quotaQueryResponse);
    }

    /**
     * 文件影像下载接口
     */
    @PostMapping({"/api/partner/v1/download","/api/partner/v2/download"})
    public LvxinResponse download(@RequestBody QuotaQueryRequest quotaQueryRequest) {
        QuotaQueryResponse quotaQueryResponse = lvxinService.queryQuota(quotaQueryRequest);
        return LvxinResponse.success(quotaQueryResponse);
    }
    /**
     * 结清证明与支付凭证申请
     */
    @PostMapping({"/api/partner/v1/certifyApply","/api/partner/v2/certifyApply"})
    public LvxinResponse certifyApply(@RequestBody QuotaQueryRequest quotaQueryRequest) {
        QuotaQueryResponse quotaQueryResponse = lvxinService.queryQuota(quotaQueryRequest);
        return LvxinResponse.success(quotaQueryResponse);
    }
    /**
     * 结清证明与支付凭证申请查询
     */
    @PostMapping({"/api/partner/v1/certifyQuery","/api/partner/v2/certifyQuery"})
    public LvxinResponse certifyQuery(@RequestBody QuotaQueryRequest quotaQueryRequest) {
        QuotaQueryResponse quotaQueryResponse = lvxinService.queryQuota(quotaQueryRequest);
        return LvxinResponse.success(quotaQueryResponse);
    }
}
