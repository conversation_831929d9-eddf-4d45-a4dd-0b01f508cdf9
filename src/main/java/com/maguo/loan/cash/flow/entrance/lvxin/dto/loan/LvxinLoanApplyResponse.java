package com.maguo.loan.cash.flow.entrance.lvxin.dto.loan;

/**
 * <AUTHOR>
 * @Description 放款申请响应
 * @Date 2024/5/21 17:35
 * @Version v1.0
 **/
public class LvxinLoanApplyResponse {
    /**
     * 优品LoanId
     */
    private String partnerOrderNo;

    private String loanApplyStatus;

    private String message;
    /**
     * 是否发送验证码 0不需要，1需要, 默认0
     */
    private Integer needVerifyCode;
    /**
     * 权益跳转地址
     */
    private String equityJumpUrl;

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public Integer getNeedVerifyCode() {
        return needVerifyCode;
    }

    public void setNeedVerifyCode(Integer needVerifyCode) {
        this.needVerifyCode = needVerifyCode;
    }

    public String getEquityJumpUrl() {
        return equityJumpUrl;
    }

    public void setEquityJumpUrl(String equityJumpUrl) {
        this.equityJumpUrl = equityJumpUrl;
    }

    public String getLoanApplyStatus() {
        return loanApplyStatus;
    }

    public void setLoanApplyStatus(String loanApplyStatus) {
        this.loanApplyStatus = loanApplyStatus;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static LvxinLoanApplyResponse fail(String loanApplyStatus, String message, String partnerOrderNo) {
        LvxinLoanApplyResponse response = new LvxinLoanApplyResponse();
        response.setLoanApplyStatus(loanApplyStatus);
        response.setMessage(message);
        response.setPartnerOrderNo(partnerOrderNo);
        return response;
    }
}
