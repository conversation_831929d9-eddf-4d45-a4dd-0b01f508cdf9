package com.maguo.loan.cash.flow.entrance.lvxin.dto.user;

import jakarta.validation.constraints.NotBlank;

/**
 * @ClassName UserCheckRequest
 * <AUTHOR>
 * @Description 用户准入请求
 * @Date 2024/3/21 15:32
 * @Version v1.0
 **/
public class UserCheckRequest {
    /**
     * 手机号 MD5大写
     */
    @NotBlank(message = "手机号 MD5大写不能为空")
    private String phoneMd5;
    /**
     * 身份证号 MD5大写
     */
    @NotBlank(message = "身份证号 MD5大写不能为空")
    private String idCardMd5;
    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空")
    private String idCard;
    /**
     * 产品类型（用于确定资方渠道）
     */
    private String productType;

    public @NotBlank(message = "手机号 MD5大写不能为空")String getPhoneMd5() {
        return phoneMd5;
    }

    public void setPhoneMd5(@NotBlank(message = "手机号 MD5大写不能为空")String phoneMd5) {
        this.phoneMd5 = phoneMd5;
    }

    public @NotBlank(message = "身份证号 MD5大写不能为空")String getIdCardMd5() {
        return idCardMd5;
    }

    public void setIdCardMd5(@NotBlank(message = "身份证号 MD5大写不能为空")String idCardMd5) {
        this.idCardMd5 = idCardMd5;
    }

    public @NotBlank(message = "身份证号不能为空")String getIdCard() {
        return idCard;
    }

    public void setIdCard(@NotBlank(message = "身份证号不能为空")String idCard) {
        this.idCard = idCard;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }
}
