package com.maguo.loan.cash.flow.entrance.lvxin.exception;


import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinResponse;
import com.maguo.loan.cash.flow.service.WarningService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;


@RestControllerAdvice(basePackages = "com.maguo.loan.cash.flow.entrance.lvxin.controller")
public class LvxinGlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(LvxinGlobalExceptionHandler.class);

    @Autowired
    private WarningService warningService;


    @ExceptionHandler(value = LvxinBizException.class)
    public LvxinResponse bizHandler(LvxinBizException e) {
        LvxinResultCode resultCode = e.getResultCode();
        logger.error("绿信业务异常", e);
        return LvxinResponse.fail(resultCode.getCode(),resultCode.getMsg());
    }

    @ExceptionHandler(value = BizException.class)
    public LvxinResponse bizHandler(BizException e) {
        logger.error("绿信业务异常:", e);
        if (e.getResultCode().getWarning()) {
            warningService.warn("绿信业务异常:" + e.getMessage());
        }
        return LvxinResponse.fail(e.getMessage());
    }


    @ExceptionHandler(value = Exception.class)
    public LvxinResponse sysHandler(Exception e) {
        warningService.warn("绿信系统异常:" + e.getMessage(), msg -> logger.error("绿信系统异常:", e));
        return LvxinResponse.fail(ResultCode.SYS_ERROR.getMsg());
    }

}
