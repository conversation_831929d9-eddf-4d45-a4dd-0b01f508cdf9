package com.maguo.loan.cash.flow.entrance.lvxin.filter;


import com.alibaba.fastjson.JSON;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.lvxin.config.LvxinFlowConfig;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinEncryptData;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinBizException;
import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinResultCode;
import com.maguo.loan.cash.flow.entrance.lvxin.filter.utils.DataCryptoUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class EncryptFilter extends HttpFilter {

    private static final Logger logger = LoggerFactory.getLogger(EncryptFilter.class);

    private LvxinFlowConfig config;

    public EncryptFilter(LvxinFlowConfig lvxinFlowConfig) {
        this.config = lvxinFlowConfig;
    }

    @Override
    protected void doFilter(HttpServletRequest req, HttpServletResponse res, FilterChain chain) throws IOException {
        String requestStr = IOUtils.toString(req.getInputStream(), StandardCharsets.UTF_8);
        logger.info("绿信 入参原始报文: {},是否跳过验签:{},{}", requestStr, config.isSkipSignVerify(), req.getRequestURL());

        try {
            LvxinEncryptData lvxinEncryptData = JsonUtil.convertToObject(requestStr, LvxinEncryptData.class);
            String decrypted;
            if (config.isSkipSignVerify() || lvxinEncryptData.getKey().equals("001")) {
                decrypted = lvxinEncryptData.getPayload();
            } else {
                // 解密业务数据
                boolean signResult = DataCryptoUtils.checkSignAndDecrypt(lvxinEncryptData, config.getClientPublicKey(), config.getServerPrivateKey());
                if (!signResult) {
                    logger.error("绿信验签失败, 原始报文: {}", requestStr);
                    throw new LvxinBizException(LvxinResultCode.SIGN_VERIFY_FAIL);
                }
                logger.info("绿信 入参解密后报文: {}", JsonUtil.toJsonString(lvxinEncryptData));
                decrypted = lvxinEncryptData.getPayload();
            }
            req.setAttribute("decryptedRequestBody", decrypted);
            logger.info("绿信 入参解密后 业务数据: {}", decrypted);
            ReplaceInputHttpRequestWrapper requestWrapper = new ReplaceInputHttpRequestWrapper(req, decrypted.getBytes(StandardCharsets.UTF_8));
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(res);
            chain.doFilter(requestWrapper, responseWrapper);

            res.setCharacterEncoding("utf-8");
            res.setContentType("application/json");

            byte[] contentBytes = responseWrapper.getContentAsByteArray();
            String responseStr = new String(contentBytes, StandardCharsets.UTF_8);
            logger.info("绿信 出参原始报文: {},{}", responseStr, req.getRequestURL());

            LvxinResponse lvxinResponse = JsonUtil.convertToObject(responseStr, LvxinResponse.class);
            lvxinResponse.setKey(config.getClientAesKeySeed());
            lvxinResponse.setChannelCode(config.getChannelCode());
            if (!config.isSkipSignVerify()) {
                //返回结果加密
                DataCryptoUtils.signAndEncrypt(lvxinResponse, config.getClientAesKeySeed(), config.getClientPublicKey(), config.getServerPrivateKey());
            }

            String jsonString = JsonUtil.toJsonString(lvxinResponse);
            logger.info("绿信 出参加密后报文: {},{}", jsonString, req.getRequestURL());

            res.setContentLength(jsonString.getBytes().length);
            ServletOutputStream outputStream = res.getOutputStream();
            outputStream.write(jsonString.getBytes());
            outputStream.flush();

        } catch (Exception e) {
            logger.error("绿信 调用异常", e);
            res.setCharacterEncoding("utf-8");
            res.setContentType("application/json");

            byte[] contentBytes = JsonUtil.toJsonString(LvxinResponse.exception(e.getMessage())).getBytes(StandardCharsets.UTF_8);
            res.setContentLength(contentBytes.length);

            ServletOutputStream outputStream = res.getOutputStream();
            outputStream.write(contentBytes);
            outputStream.flush();
        }
    }
    /**
     * 流量私钥
     */
    private static final String privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANRRAwHLpD660SmJqD+yZHieXG6uYvTWyvRb4xV/fWrWfawPHiFBixQV4IUpqsUFU1THk5aDqZjwOEIMRwdqWYkW/5UG4xsLe9112ezOE57sra8UUrsJjDl9g8T+ln4OZO1z+Wd9+rNoN1a/vM+ZoERDzp9ahgaIZWN90Tmdo4lTAgMBAAECgYAO4ts2HgQXDdwqcGdmh+h5eIj8jaeMiR7TwTmLwVOW0REgpjKznDfXIa2LSJ+bjsMH97JGZFbYzUb0go3cXD2IuWWqK5UmM9kMuiaJrPx60qUU0dlYWRI0EiZ5YvSNHact6b8Z8Rt6fywwrSn2NdTsmTN/z0kWnjlDM3ZNTIsD+QJBAPOnhOq2oZtHMKyEUUEY/r06QXo65Em2ohCdkmuJMltHt3LxHy/PH+GXjH3E7D4orb+gGBmvaG/i5r6R+B2mcb8CQQDfEwDUu3H5E/CFoPDfl6KEsluODMv5PlLSxHYhSj3Q2Sd3lpcST7M+8HhzoOZSUqBe2UmJGnslf1f985x8y6VtAkEAr/IPNGKvbm7JAjsGZejDwyd3MummAmjkvPTcT9UhzPKL0W+3IpICJqiGJif2pKhBV3gnDx0wYGNMVt8XeWnBBwJAMCBObpbeGOmXW40ESojRVCWSoQvbubMbdUNTZQ06VuNyo1dG0nmFZ0/D/NY6uon+9VeViNHAva9CkZRML5RT5QJBAJfziCRL3Da78GBWnBP0tgm/fofmV8giPoYFcgRCKG2UT6nA2p6PWw8TKvC/83XvKN5e9YV2ysKOVBrM1G7ZbL0=";
    /**
     * 流量公钥
     */
    private static final String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDUUQMBy6Q+utEpiag/smR4nlxurmL01sr0W+MVf31q1n2sDx4hQYsUFeCFKarFBVNUx5OWg6mY8DhCDEcHalmJFv+VBuMbC3vdddnszhOe7K2vFFK7CYw5fYPE/pZ+DmTtc/lnffqzaDdWv7zPmaBEQ86fWoYGiGVjfdE5naOJUwIDAQAB";

    /**
     * jh私钥
     */
    private static final String tcPrivateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMxFPMsWs66EQqSAl7HqSTJ3i3bj5hEla4IiGm2Du5UwYwwH/bmaXj4TEl4Ly/oAUz5S3fKxBrxd3xQxOXQj6dgciHcCRqAKU5McqnnqBKrwujR0ESdnXigjnBSQIUjwxEnHrkhGVZorYsaRAVproC8QUYIe2iafXJLPAhkcXiI7AgMBAAECgYEAxzsAgFQ0DBKTrT5+tdh8a+yM6fh9fFuy78mzXP0PCAOZlzJ0FYKqSIDxeSioWb3XcwH/16UxnKcx3M/l6ZDdkVyunGu6BBnKlYyUb4UV52Pw/CYndkLU+nJxRLcgZwYRczrl21eKHZgVhzoIpxHp+yf943CB39Nmm4zuNl4Sm4ECQQDlfpBP2uGeq7BQYZV5PpBkwtwT0E+ftCu/aP7OHjzL5QERiGh7n247xanNiNqYrJXU765ngX/2NNxTPBhTFKt9AkEA49zhZqcEw+Y9DuNXTEphDzuXFPHYsy1s0n+hzadrmPOPaA/ephmadyFwxjoETKKtWF/LdM9TdrLFVf29UybCFwJAP+n7rwG6jEIsnHZQ9q9h724FdM8zHbtD488OB8GMFQz2OzfOQX+pZ6QpIkDXAGYwZFlMLS93v84A/n1iGOFM0QJAKRYFyoWpFU2pisqxTf9HWwDKsCsfgMdbDOWYnkVm4FBidkgORHLvaDWuaP/DELJwgTPcsrZDfW6kqGm1Po4JhwJASGeI3Yy87ymTpm2cOZVTb3UlJsWmcgyD5xHW0mOqdp2TUfLZXPcRAqVe8OCT0h/IRxhqSj/l6F1MGQw+bGpMvA==";
    /**
     * jh公钥
     */
    private static final String tcPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDMRTzLFrOuhEKkgJex6kkyd4t24+YRJWuCIhptg7uVMGMMB/25ml4+ExJeC8v6AFM+Ut3ysQa8Xd8UMTl0I+nYHIh3AkagClOTHKp56gSq8Lo0dBEnZ14oI5wUkCFI8MRJx65IRlWaK2LGkQFaa6AvEFGCHtomn1ySzwIZHF4iOwIDAQAB";

    public static void main(String[] args) throws Exception {
        String aesKey="";
        String data = "{\n" +
            "    \"channelCode\": \"LVXIN\",\n" +
            "    \"payload\": \"/X1uJN3Cu7P2eYfuc7skxe/CL+5gpVRcaRM4bDKdq86DxZe57Y27Pi5Hs/t6b1bBiB3+OVyCBhtT8mtZWCvE7Lmu7VIAadMuLrGZXRJgaZoornn5xsQ4QKGkyEL73iSzsz7/mjF0ZtyAIY7Hz+e4vhpAs1pkFpRD+yXmQSiUsdcpkvqizWM8FPRNS8zTUm81aJ+EVrT6OpQvsWzc0HXSzLxXC9s9x3sGn/bWQNooI9t0CS2gtzOy6nq0ON1mI5ig\",\n" +
            "    \"sign\": \"FZRXiwojbNSJayTplCWxie9XcuubxbYyUFv5gej+gY3bg9XA51whlbD+ecYV2fHth8qYuTreLZ6oUWvqB1QY7cEcwU/T1w7u5yHt4wj+hEkbrSTJt6O72L70rLb1KTnxKsLjBbQzWiwkEtAJpy8dGMT2i1HCTT2meKSllrjCf2k=\",\n" +
            "    \"code\": \"200\",\n" +
            "    \"message\": \"成功\"\n" +
            "}";
        LvxinEncryptData requestData = new LvxinEncryptData();
        requestData.setChannelCode("ZHPOO1");
        requestData.setPayload(data);
        System.out.println("aesKey:      "+aesKey);
        LvxinEncryptData requestData1 = DataCryptoUtils.signAndEncrypt(requestData, aesKey, publicKey, tcPrivateKey);
        String requestData1Json = JSON.toJSONString(requestData1);
        System.out.println("加密后响应:      "+requestData1Json);
        boolean b = DataCryptoUtils.checkSignAndDecrypt(requestData1, tcPublicKey, privateKey);
        if(b){
            System.out.println("验签成功");
            System.out.println("解密后响应:      "+JSON.toJSONString(requestData1));
        }else{
            System.out.println("验签失败");
        }
    }
}
