package com.maguo.loan.cash.flow.entrance.lvxin.service;


import com.jinghang.common.util.DateUtil;
import com.maguo.loan.cash.flow.common.CallBackException;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.CreditQueryResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinLoanQueryResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinRepayPlanQueryResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.enums.LvxinLoanStatus;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.service.LoanService;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.RepayPlanService;
import com.maguo.loan.cash.flow.service.callback.AbstractCallbackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @Description 绿信 回调服务
 * @Date 2024/3/21 14:37
 * @Version v1.0
 **/
@Service
public class LvxinCallbackService extends AbstractCallbackService {
    private static final Logger logger = LoggerFactory.getLogger(LvxinCallbackService.class);
    public static final long SEVEN = 7L;

    @Autowired
    private OrderService orderService;
    @Autowired
    private LoanService loanService;
    @Autowired
    private PreOrderRepository preOrderRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private RepayPlanService repayPlanService;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private LvxinService lvxinService;
    @Autowired
    private LvxinRequestService requestService;

    @Override
    public FlowChannel getFlowChannel() {
        return FlowChannel.LVXIN;
    }

    /**
     * 处理回调
     */
    @Override
    public void processCallback(CallBackDTO callBackDTO) {
        switch (callBackDTO.getCallbackState()) {
            case RISK_REJECT, RISK_PASS -> {
                String businessId = callBackDTO.getBusinessId();
                //审批结果回调
                approvalFeedBackNew(businessId);
            }
            case CREDIT_FAIL -> {
                String businessId = callBackDTO.getBusinessId();
                Order order = orderService.findById(businessId);
                //放款回调
                loanResultFeedBack(null, order, businessId);
            }
            case LOAN_CANCEL -> {
                String orderId = callBackDTO.getBusinessId();
                //授信回调
                approvalFeedBack(orderId);
            }
            case LOAN_PASS, LOAN_FAIL -> {
                String orderId = callBackDTO.getBusinessId();
                Order order = orderService.findById(orderId);
                Loan loan = loanRepository.findByOrderId(order.getId());
                loanResultFeedBack(loan, order, orderId); //放款结果
                if (loan != null && ProcessState.SUCCEED == loan.getLoanState()) {
                    repayPlanFeedBack(loan.getId()); //还款计划及权益计划
                }
            }
            case RIGHT_ORDER_SUCCESS,
                 RIGHT_ORDER_CANCELED,
                 RIGHT_ORDER_REFUND -> {
                //还款计划
                String loanId = callBackDTO.getBusinessId();
                repayPlanFeedBack(loanId);
            }
            case OVERDUE -> {
                String repayPlanId = callBackDTO.getBusinessId();
                RepayPlan repayPlan = repayPlanService.findById(repayPlanId);
                repayPlanFeedBack(repayPlan.getLoanId());
            }
            case REPAID -> {
                String crrId = callBackDTO.getBusinessId();
                CustomRepayRecord customRepayRecord = customRepayRecordRepository.findById(crrId).orElseThrow();
                // repayPlanFeedBack(customRepayRecord.getLoanId());
            }
            case CLEAR -> {
                String orderId = callBackDTO.getBusinessId();
                Loan loan = loanService.findByOrderId(orderId);
                repayPlanFeedBack(loan.getId());
            }
            default -> {
            }
        }
    }

    /**
     * 审核结果回调
     */
    private void approvalFeedBackNew(String riskId) {
        PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElseThrow();
        CreditQueryResponse request = lvxinService.creditResultQuery(preOrder);
        try {
            requestService.request(request);
        } catch (Exception e) {
            logger.error("绿信流量授信结果回调异常，riskId:" + riskId, e);
            throw new CallBackException(FlowChannel.LVXIN, e.getMessage());
        }
    }

    /**
     * 审核结果回调
     */
    private void approvalFeedBack(String orderId) {
        Order order = orderService.findById(orderId);
        PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(order.getOuterOrderId(), order.getFlowChannel()).orElseThrow();
        CreditQueryResponse request = lvxinService.creditResultQuery(preOrder);
        try {
            requestService.request(request);
        } catch (Exception e) {
            logger.error("绿信流量授信结果回调异常，orderId:" + orderId, e);
            throw new CallBackException(FlowChannel.LVXIN, e.getMessage());
        }
    }

    /**
     * 放款结果回调
     */
    public void loanResultFeedBack(Loan loan, Order order, String businessId) {
        try {
            if (loan == null) {
                LvxinLoanQueryResponse loanResult = new LvxinLoanQueryResponse();
                loanResult.setLoanGid(""); //不能给null
                loanResult.setPartnerUserId(order.getOuterOrderId());
                loanResult.setPartnerOrderNo(order.getId());
                loanResult.setLoanApplyStatus(LvxinLoanStatus.LOAN_FAIL.getCode());
                //封禁期:D+1天的早上7点
                LocalDateTime tomorrow = LocalDate.now().plusDays(1L).atStartOfDay().plusHours(SEVEN);
                loanResult.setClosureTime(DateUtil.toMilliseconds(tomorrow));
                loanResult.setRateType(2); //irr
                loanResult.setMessage("资金匹配失败");
                requestService.request(loanResult);
            } else {
                LvxinLoanQueryResponse loanResult = lvxinService.queryLoanResult(loan, order);
                requestService.request(loanResult);
            }
        } catch (Exception e) {
            logger.error("绿信流量放款结果回调异常，businessId:" + businessId, e);
            throw new CallBackException(FlowChannel.LVXIN, e.getMessage());
        }
    }

    /**
     * 推送还款计划及权益计划
     */
    public void repayPlanFeedBack(String loanId) {
        try {
            LvxinRepayPlanQueryResponse loanPlanQueryResponse = lvxinService.queryRepayPlan(loanId);
            requestService.request(loanPlanQueryResponse);
        } catch (Exception e) {
            logger.error("绿信流量推送还款计划异常，businessId:" + loanId, e);
            throw new CallBackException(FlowChannel.LVXIN, e.getMessage());
        }
    }
}

