package com.maguo.loan.cash.flow.entrance.ppd.controller;


import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinBizException;
import com.maguo.loan.cash.flow.entrance.ppd.dto.ApiResult;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.job.agreement.SupplementaryAgreementJob;
import com.maguo.loan.cash.flow.job.jh.LoanJhJob;
import com.maguo.loan.cash.flow.job.susupend.RouteCreditBatch;
import com.maguo.loan.cash.flow.job.susupend.SuspendActivationJob;
import com.maguo.loan.cash.flow.util.SftpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/lvxin/api")
public class PpdTest1Controller {

    @Autowired
    private SftpUtils sftpUtils;



    @PostMapping("/test")
    public ApiResult test(@RequestBody Map<String,String> map) {

        try {
            InputStream headImage = sftpUtils.downloadAsStream(map.get("headImage"));
        } catch (Exception e) {
            if (e.getMessage().contains("No such file")) {
                throw new LvxinBizException("未在ftp中拉取到身份证正反面文件,请先上传证件信息");
            }
            throw new LvxinBizException("下载拍拍身份证正反面异常");
        }
        return null;
    }

    @PostMapping("/test1")
    public ApiResult test1(@RequestBody Map<String,String> map) {

        try {
            String url1 = "/home/<USER>/upload/video/20250703/PPD20250703005/id_140406199507021030_01.jpg";
            String url2 = "/home/<USER>/upload/video/20250703/PPD20250703005/id_140406199507021030_02.jpg";
            String url3 = "/home/<USER>/upload/video/20250703/PPD20250703005/id_140406199507021030_03.jpg";
            sftpUtils.downloadAsStream(url1);
            sftpUtils.downloadAsStream(url2);
            sftpUtils.downloadAsStream(url3);

        } catch (Exception e) {
            if (e.getMessage().contains("No such file")) {
                throw new LvxinBizException("未在ftp中拉取到身份证正反面文件,请先上传证件信息");
            }
            throw new LvxinBizException("下载拍拍身份证正反面异常");
        }
        return null;
    }

    @Autowired
    SupplementaryAgreementJob creditBatch;
    @PostMapping("/test2")
    public ApiResult test2(@RequestBody Map<String,String> map) {

        try {
            JobParam jobParam=new JobParam();
            List<String> list=new ArrayList<>();
            list.add("LO250825094045607107798543140852");
            list.add("LO250828153304426119106828088160");
            jobParam.setLoanIds(list);
            jobParam.setChannel("LVXIN");
            creditBatch.doJob(jobParam);

        } catch (Exception e) {
            if (e.getMessage().contains("No such file")) {
                throw new LvxinBizException("未在ftp中拉取到身份证正反面文件,请先上传证件信息");
            }
            throw new LvxinBizException("下载拍拍身份证正反面异常");
        }
        return null;
    }
}
