package com.maguo.loan.cash.flow.entrance.ppd.enums;


/**
 * 还款结果状态
 *
 * <AUTHOR>
 */
public enum PpdRepayStatus {

    /**
     * 成功
     */
    SUCCESS("00", "成功"),
    FAIL("01", "失败"),
    FAILED("01", "授信超过有效期"),
    PROCESSING("99", "处理中"),
    ERROR("10", "系统异常"),
    CLEARED("20", "项目(当期)已结清");

    private final String code;
    private final String desc;

    PpdRepayStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
