package com.maguo.loan.cash.flow.job.jh;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.annotation.AlarmAnno;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.recc.ReccApplyDto;
import com.jinghang.capital.api.dto.recc.ReccResultDto;
import com.jinghang.capital.api.dto.recc.ReccType;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.config.LvXinConfig;
import com.maguo.loan.cash.flow.config.LvXinNewSFTPConfig;
import com.maguo.loan.cash.flow.entity.vo.RepayPlanVo;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.remote.core.FinReccService;
import com.maguo.loan.cash.flow.service.JHReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.job.jh.RepayPlanJhJob
 * @作者 Mr.sandman
 * @时间 2025/05/29 16:49
 */
@Component
@JobHandler("repayPlanJhJob")
public class RepayPlanJhJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(RepayPlanJhJob.class);

    @Autowired
    private JHReconService jhReconService;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private LvXinConfig lvXinConfig;
    @Autowired
    private LvXinNewSFTPConfig lvXinNewSFTPConfig;
    @Autowired
    private FinReccService finReccService;
    @Override
    public void doJob( JobParam jobParam ) {
        // 参数要传 {"channel":"LVXIN","bankChannel":"CYBK/HXBK"}
        logger.info("生成绿信还款计划csv开始");
        try {
            // 优先从 JobParam 获取 startDate，如果没有则使用当前日期减一天
            LocalDate localDate;
            if ( Objects.nonNull(jobParam) && jobParam.getStartDate() != null) {
                localDate = jobParam.getStartDate();
            } else {
                localDate = LocalDate.now().minusDays(1);
            }
            jobParam.setTaskHandler("repayPlanJhJob");
            jobParam.setTaskDescription("还款计划文件-绿信");
            jobParam.setStartDate(localDate);
            String  yesterday = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = "plan_" + yesterday + ".csv";
            String okFileName = "plan_" + yesterday + ".ok";
            String remoteDir = null;
            String includingDir =null;
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                remoteDir = lvXinConfig.getLoanSftpPath() + yesterday + "/";
                includingDir = lvXinConfig.getIncludingEquitySftpPath() + yesterday + "/";
            } else if ( jobParam.getBankChannel() == BankChannel.HXBK ) {
                remoteDir = lvXinNewSFTPConfig.getLoanSftpPath() + yesterday + "/";
                includingDir = lvXinNewSFTPConfig.getIncludingEquitySftpPath() + yesterday + "/";
            }
            FlowChannel flowChannel = FlowChannel.getFlowChannel(jobParam.getChannel());
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                ReccApplyDto reccApplyDto = new ReccApplyDto();
                reccApplyDto.setChannel(jobParam.getBankChannel());
                reccApplyDto.setReccDay(localDate);
                reccApplyDto.setReccType(ReccType.PLAN);
                logger.info("lvxin还款计划txt上传,对资对账文件接口参数:{}", JsonUtil.toJsonString(reccApplyDto));
                RestResult<ReccResultDto> query = finReccService.query(reccApplyDto);
                logger.info("绿信还款计划txt上传,对资对账文件接口返回信息:{}", JsonUtil.toJsonString(query));
                if (!(query.isSuccess() && query.getData().getStatus() == ProcessStatus.SUCCESS)) {
                    logger.info("绿信还款计划txt上传失败 对资对账文件接口调用失败:{}", query.getData().getFailMsg());
                    throw new BizException("跑批结束,对资对账文件异常", ResultCode.BIZ_ERROR);
                }
            }
            logger.info("获取绿信还款计划数据 时间: {}, 流量渠道: {}, 资方渠道: {}", localDate, flowChannel, jobParam.getBankChannel());

            //  获取数据
            List<RepayPlanVo> repayPlanVos = jhReconService.getRepayPlanReconFile(localDate,flowChannel,jobParam.getBankChannel(), IsIncludingEquity.N);
            List<RepayPlanVo> includingRepayPlanVos = jhReconService.getRepayPlanReconFile(localDate,flowChannel,jobParam.getBankChannel(), IsIncludingEquity.Y);

            // 生成csv文件流
            ByteArrayOutputStream stream = generateCsvToStream(repayPlanVos);
            ByteArrayOutputStream includingStream = generateCsvToStream(includingRepayPlanVos);

            // 上传到 SFTP 长银沿用之前的 湖消新增
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                sftpUtils.uploadStreamToSftp(stream, fileName, remoteDir);
                // 上传.ok文件
                sftpUtils.uploadStreamToSftp(new ByteArrayOutputStream(), okFileName, remoteDir);
                sftpUtils.uploadStreamToSftp(includingStream, fileName, includingDir);
                sftpUtils.uploadStreamToSftp(new ByteArrayOutputStream(), okFileName, includingDir);
                logger.info("绿信-长银还款计划csv上传成功");
            } else if ( jobParam.getBankChannel() == BankChannel.HXBK ) {
                sftpUtils.uploadStreamToNewSftp(stream, fileName, remoteDir);
                // 上传.ok文件
                sftpUtils.uploadStreamToNewSftp(new ByteArrayOutputStream(), okFileName, remoteDir);
                sftpUtils.uploadStreamToNewSftp(includingStream, fileName, includingDir);
                sftpUtils.uploadStreamToNewSftp(new ByteArrayOutputStream(), okFileName, includingDir);
                logger.info("绿信-湖消还款计划csv上传成功");
            }
            //回调
            jhReconService.saveTaskMonitoringData(jobParam,true,null);
        } catch (Exception e) {
            logger.error("绿信还款计划csv上传失败", e);
            e.printStackTrace();
            //回调
            jhReconService.saveTaskMonitoringData(jobParam,false,e);
        }

    }


    private static ByteArrayOutputStream generateCsvToStream( List<RepayPlanVo> data ) throws IOException {
        String[] headers = {
            "对方业务号", "借据号", "期数", "到期日", "期供金额", "本金金额",
            "利息金额", "罚息金额", "复利金额", "担保费金额", "担保费逾期费用",
            "咨询费"
        };

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
        BufferedWriter bw = new BufferedWriter(writer);

        // 写表头
        bw.write(String.join(",", headers));
        bw.newLine();

        // 写数据（你替换的逻辑）
        for (RepayPlanVo loan : data) {
            bw.write(String.join(",", Arrays.asList(
                safe(loan.getOutApplSeq()), safe(loan.getLoanNo()), safe(loan.getThr()),
                safe(loan.getDueDt()), safe(loan.getInstmAmt()), safe(loan.getPrcpAmt()),
                safe(loan.getIntAmt()), safe(loan.getOdIntAmt()), safe(loan.getCommIntAmt()),
                safe(loan.getGuaranteeFeeAmt()), safe(loan.getGuaranteeFeeOdAmt()), safe(loan.getConsultFee()))));
            bw.newLine();
        }

        bw.flush();
        return outputStream;
    }

    // 处理 null 的字段
    private static String safe(String val) {
        return val == null ? "" : val;
    }

    private static String safe( BigDecimal val ) {
        return val == null ? "" : val.toPlainString();
    }

}
