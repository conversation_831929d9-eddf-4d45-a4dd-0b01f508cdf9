package com.maguo.loan.cash.flow.remote.cardbin.impl;


import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.BankCardRelation;
import com.maguo.loan.cash.flow.entity.BankList;
import com.maguo.loan.cash.flow.remote.cardbin.CardBin;
import com.maguo.loan.cash.flow.remote.cardbin.CardBinService;
import com.maguo.loan.cash.flow.repository.BankCardRelationRepository;
import com.maguo.loan.cash.flow.repository.BankListRepository;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;

@Service
public class AlipayCardBinService implements CardBinService {
    private static final Logger log = LoggerFactory.getLogger(AlipayCardBinService.class);

    @Autowired
    private BankListRepository bankListRepository;

    @Autowired
    private BankCardRelationRepository bankCardRelationRepository;

    @Autowired
    private LockService lockService;

    public static final int LOCK_WAIT_SECOND = 2;
    public static final int LOCK_RELEASE_SECOND = 8;

    @Value("${alipay.cardbin.service.url}")
    private String url;

    @Override
    public CardBin query(String cardNo) {

        String cardNoPrefixSix = cardNo.substring(0, 6);
        BankCardRelation bankCardRelation = bankCardRelationRepository.findByCardNoPrefixSix(cardNoPrefixSix).orElse(null);
        if (Objects.nonNull(bankCardRelation)) {
            AlipayCardBinResult result = new AlipayCardBinResult();
            result.setBank(bankCardRelation.getAbbr());
            result.setCardType(bankCardRelation.getCardType());
            result.setValidated(true);
            return getCardBinResult(result);
        } else {
            String requestUrl = url + cardNo;
            try {
                String resp = HttpUtil.get(requestUrl);
                log.info("alipay cardBin 接口,url:{}, response:{}", requestUrl, resp);

                var result = JsonUtil.convertToObject(resp, AlipayCardBinResult.class);

                return getCardBinResult(result);
            } catch (Exception e) {
                log.error("alipay cardBin接口接口异常, url: {}", requestUrl, e);
            }
        }
        return null;
    }


    private CardBin getCardBinResult(AlipayCardBinResult result) {
        if (result == null || !result.isValidated()) {
            return null;
        }
        CardBin cardBin = new CardBin();
        cardBin.setBankAbbr(result.getBank());
        cardBin.setCredit(result.getCardType().equals("CC"));
        BankList bank = findBank(cardBin.getBankAbbr());
        if (bank != null) {
            cardBin.setName(bank.getName());
            cardBin.setShortName(bank.getShortName());
            cardBin.setBankAbbr(bank.getAbbr());
            cardBin.setIconUrl(bank.getIconUrl());
            if ("ok".equals(result.getStat())) {
                this.saveBankCardRelation(result);
            }
        }
        return cardBin;
    }

    private void saveBankCardRelation(AlipayCardBinResult result) {
        String cardNoPrefixSix = result.getKey().substring(0, 6);
        Locker lock = lockService.getLock("bind_card_releation_" + cardNoPrefixSix);
        try {
            BankCardRelation bankCardRelation = bankCardRelationRepository.findByCardNoPrefixSix(cardNoPrefixSix)
                .orElse(null);
            if (Objects.isNull(bankCardRelation)) {
                boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
                if (locked) {
                    bankCardRelation = bankCardRelationRepository.findByCardNoPrefixSix(cardNoPrefixSix)
                        .orElse(null);
                    if (Objects.isNull(bankCardRelation)) {
                        bankCardRelation = new BankCardRelation();
                        bankCardRelation.setAbbr(result.getBank());
                        bankCardRelation.setCardType(result.getCardType());
                        bankCardRelation.setCardNoPrefixSix(result.getKey().substring(0, 6));
                        bankCardRelation.setCreatedTime(LocalDateTime.now());
                        bankCardRelation.setUpdatedTime(LocalDateTime.now());
                        bankCardRelationRepository.save(bankCardRelation);
                    }
                }
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }
    }

    private BankList findBank(String code) {
        return bankListRepository.findByAbbrOrOldAbbr(code).orElse(null);
    }


    static class AlipayCardBinResult {
        private boolean validated;
        private String cardType;
        private String bank;
        private String key;
        private String stat;

        public boolean isValidated() {
            return validated;
        }

        public void setValidated(boolean validated) {
            this.validated = validated;
        }

        public String getCardType() {
            return cardType;
        }

        public void setCardType(String cardType) {
            this.cardType = cardType;
        }

        public String getBank() {
            return bank;
        }

        public void setBank(String bank) {
            this.bank = bank;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getStat() {
            return stat;
        }

        public void setStat(String stat) {
            this.stat = stat;
        }
    }

}
