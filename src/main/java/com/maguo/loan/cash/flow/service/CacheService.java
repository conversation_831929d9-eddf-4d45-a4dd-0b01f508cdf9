package com.maguo.loan.cash.flow.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务（支持LocalDateTime序列化）
 */
@Slf4j
@Component
public class CacheService {

    private final RedissonClient redisson;

    @Autowired
    private ObjectMapper objectMapper;

    public CacheService(RedissonClient redisson) {
        this.redisson = redisson;
    }

    /**
     * 缓存对象（带过期时间）
     */
    public void put(String key, Object value, long ttl, TimeUnit timeUnit) {
        try {
            if (needSpecialSerialization(value)) {
                String jsonValue = objectMapper.writeValueAsString(value);
                redisson.getBucket(key).set(jsonValue, ttl, timeUnit);
                log.debug("特殊序列化存入缓存 key: {}, ttl: {} {}", key, ttl, timeUnit);
            } else {
                redisson.getBucket(key).set(value, ttl, timeUnit);
                log.debug("普通序列化存入缓存 key: {}, ttl: {} {}", key, ttl, timeUnit);
            }
        } catch (JsonProcessingException e) {
            log.error("缓存序列化失败 key: {}", key, e);
            throw new RuntimeException("缓存序列化失败", e);
        }
    }

    /**
     * 缓存对象（永不过期）
     */
    public void put(String key, Object value) {
        try {
            if (needSpecialSerialization(value)) {
                String jsonValue = objectMapper.writeValueAsString(value);
                redisson.getBucket(key).set(jsonValue);
                log.debug("特殊序列化存入缓存 key: {}", key);
            } else {
                redisson.getBucket(key).set(value);
                log.debug("普通序列化存入缓存 key: {}", key);
            }
        } catch (JsonProcessingException e) {
            log.error("缓存序列化失败 key: {}", key, e);
            throw new RuntimeException("缓存序列化失败", e);
        }
    }

    /**
     * 缓存对象（Duration方式）
     */
    public void put(String key, Object value, Duration duration) {
        try {
            if (needSpecialSerialization(value)) {
                String jsonValue = objectMapper.writeValueAsString(value);
                redisson.getBucket(key).set(jsonValue, duration);
                log.debug("特殊序列化存入缓存 key: {}, duration: {}", key, duration);
            } else {
                redisson.getBucket(key).set(value, duration);
                log.debug("普通序列化存入缓存 key: {}, duration: {}", key, duration);
            }
        } catch (JsonProcessingException e) {
            log.error("缓存序列化失败 key: {}", key, e);
            throw new RuntimeException("缓存序列化失败", e);
        }
    }

    /**
     * 获取缓存对象
     */
    public Object get(String key) {
        return redisson.getBucket(key).get();
    }

    /**
     * 获取缓存对象并转换为指定类型
     */
    public <T> T get(String key, Class<T> clazz) {
        try {
            Object value = redisson.getBucket(key).get();
            if (value == null) {
                return null;
            }

            // 如果是字符串且目标类型不是String，尝试反序列化
            if (value instanceof String && !clazz.equals(String.class)) {
                return objectMapper.readValue((String) value, clazz);
            }

            return clazz.cast(value);
        } catch (JsonProcessingException e) {
            log.error("缓存反序列化失败 key: {}, class: {}", key, clazz.getName(), e);
            throw new RuntimeException("缓存反序列化失败", e);
        } catch (ClassCastException e) {
            log.error("缓存类型转换失败 key: {}, expected: {}", key, clazz.getName(), e);
            throw new RuntimeException("缓存类型转换失败", e);
        }
    }

    /**
     * 获取列表类型的缓存
     */
    public <T> List<T> getList(String key, Class<T> elementType) {
        try {
            Object value = redisson.getBucket(key).get();
            if (value == null) {
                return null;
            }

            // 如果是字符串，尝试反序列化为列表
            if (value instanceof String) {
                JavaType javaType = objectMapper.getTypeFactory()
                    .constructCollectionType(List.class, elementType);
                return objectMapper.readValue((String) value, javaType);
            }

            // 如果已经是列表，直接返回
            if (value instanceof List) {
                @SuppressWarnings("unchecked")
                List<T> result = (List<T>) value;
                return result;
            }

            throw new RuntimeException("缓存类型不匹配，期望List<" + elementType.getName() + ">");

        } catch (JsonProcessingException e) {
            log.error("缓存列表反序列化失败 key: {}, elementType: {}", key, elementType.getName(), e);
            throw new RuntimeException("缓存列表反序列化失败", e);
        }
    }

    /**
     * 使用TypeReference获取复杂泛型类型
     */
    public <T> T get(String key, TypeReference<T> typeReference) {
        try {
            Object value = redisson.getBucket(key).get();
            if (value == null) {
                return null;
            }

            if (value instanceof String) {
                return objectMapper.readValue((String) value, typeReference);
            }

            return objectMapper.convertValue(value, typeReference);
        } catch (JsonProcessingException e) {
            log.error("缓存反序列化失败 key: {}, typeReference: {}", key, typeReference.getType(), e);
            throw new RuntimeException("缓存反序列化失败", e);
        }
    }

    /**
     * 键值是否存在
     */
    public boolean existKey(String key) {
        return redisson.getBucket(key).isExists();
    }

    /**
     * 删除缓存
     */
    public boolean delete(String key) {
        boolean result = redisson.getBucket(key).delete();
        if (result) {
            log.debug("删除缓存成功 key: {}", key);
        }
        return result;
    }

    /**
     * 设置过期时间
     */
    public boolean expire(String key, long ttl, TimeUnit timeUnit) {
        return redisson.getBucket(key).expire(ttl, timeUnit);
    }

    /**
     * 设置过期时间（Duration方式）
     */
    public boolean expire(String key, Duration duration) {
        return redisson.getBucket(key).expire(duration);
    }

    /**
     * 获取剩余过期时间
     */
    public long getExpire(String key, TimeUnit timeUnit) {
        return redisson.getBucket(key).remainTimeToLive();
    }

    /**
     * 判断是否需要特殊序列化（检查是否包含LocalDateTime字段）
     */
    private boolean needSpecialSerialization(Object value) {
        if (value == null) {
            return false;
        }

        // 如果是集合，检查元素类型
        if (value instanceof Collection) {
            Collection<?> collection = (Collection<?>) value;
            if (!collection.isEmpty()) {
                Object firstElement = collection.iterator().next();
                return hasLocalDateTimeField(firstElement);
            }
            return false;
        }

        // 单个对象
        return hasLocalDateTimeField(value);
    }

    /**
     * 检查对象是否包含LocalDateTime字段
     */
    private boolean hasLocalDateTimeField(Object obj) {
        if (obj == null) {
            return false;
        }

        Class<?> clazz = obj.getClass();
        // 检查常见Java类型，避免不必要的反射
        if (isSimpleType(clazz)) {
            return false;
        }

        // 使用反射检查所有字段
        while (clazz != null && clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                if (LocalDateTime.class.equals(field.getType())) {
                    return true;
                }
            }
            clazz = clazz.getSuperclass();
        }
        return false;
    }

    /**
     * 判断是否为简单类型（不需要特殊序列化）
     */
    private boolean isSimpleType(Class<?> clazz) {
        return clazz.isPrimitive() ||
            clazz.equals(String.class) ||
            clazz.equals(Integer.class) ||
            clazz.equals(Long.class) ||
            clazz.equals(Double.class) ||
            clazz.equals(Float.class) ||
            clazz.equals(Boolean.class) ||
            clazz.equals(Byte.class) ||
            clazz.equals(Short.class) ||
            clazz.equals(Character.class);
    }

    /**
     * 批量删除缓存（前缀匹配）
     */
    public long deleteByPattern(String pattern) {
        return redisson.getKeys().deleteByPattern(pattern);
    }
}
