package com.maguo.loan.cash.flow.service;


import com.maguo.loan.cash.flow.dto.ContractFileDTO;
import com.maguo.loan.cash.flow.entity.AgreementShow;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.enums.ContractParamEnum;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.AgreementShowRepository;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.agreement.AgreementShowService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同服务
 * 3.7.1
 */
@Service
public class ContractService {

    private static final Logger logger = LoggerFactory.getLogger(ContractService.class);

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private AgreementShowService agreementShowService;

    @Autowired
    private LoanRepository loanRepository;


    @Autowired
    private FileService fileService;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Autowired
    private AgreementShowRepository agreementShowRepository;

    @Autowired
    private PreOrderRepository preOrderRepository;




    public List<UserFile> findUserCreditSign(String userId) {
        return userFileRepository.findByUserIdAndLoanStage(userId, LoanStage.CREDIT);
    }

    public List<UserFile> findUserLoanSign(String userId) {
        return userFileRepository.findByUserIdAndLoanStage(userId, LoanStage.LOAN);
    }

    /**
     * 带流量渠道场景贷前协议模版
     */
    public List<ContractFileDTO> findPerLoanContractFiles(FlowChannel flowChannel, String param) {
        List<ContractFileDTO> agreementShows = new ArrayList<>();
        List<AgreementShow> agreements = agreementShowService.getAgreements(flowChannel, param);
        agreements.forEach(agreementShow -> {
            ContractFileDTO contractFileDTO = new ContractFileDTO();
            contractFileDTO.setAgreementName(agreementShow.getAgreementName());
            contractFileDTO.setAgreementUrl(agreementShow.getAgreementUrl());
            agreementShows.add(contractFileDTO);
        });
        return agreementShows;
    }

    /**
     * 全流量通用场景贷前协议模版
     */
    public List<ContractFileDTO> findContractFiles(ContractParamEnum param) {
        List<ContractFileDTO> agreementShows = new ArrayList<>();
        List<AgreementShow> agreements = agreementShowService.getAgreements(null, param.toString());
        agreements.forEach(agreementShow -> {
            ContractFileDTO contractFileDTO = new ContractFileDTO();
            contractFileDTO.setAgreementName(agreementShow.getAgreementName());
            contractFileDTO.setAgreementUrl(agreementShow.getAgreementUrl());
            contractFileDTO.setAgreementNo(agreementShow.getAgreementNo());
            agreementShows.add(contractFileDTO);
        });
        return agreementShows;
    }



    /**
     * 通用贷后协议
     */

    public List<ContractFileDTO> findPostLoanContractFiles(Order order) {

        ArrayList<ContractFileDTO> contractFileDTOS = new ArrayList<>();
        // 贷后协议
        Loan loan = loanRepository.findByOrderId(order.getId());
        String relatedId = loan.getLoanRecordId();
        List<UserFile> creditFiles = agreementSignRelationRepository.getUserFiles(loan.getCreditId(), LoanStage.CREDIT);
        List<UserFile> userFiles = agreementSignRelationRepository.getUserFiles(relatedId, LoanStage.LOAN);
        creditFiles.addAll(userFiles);
        mergeStream(creditFiles, contractFileDTOS);

        return contractFileDTOS;
    }

    public List<ContractFileDTO> queryPostLoanContractFile(Order order) {

        ArrayList<ContractFileDTO> contractFileDTOS = new ArrayList<>();
        // 贷后协议
        Loan loan = loanRepository.findByOrderId(order.getId());
        String relatedId = loan.getLoanRecordId();
        String riskId = order.getRiskId();
        if (StringUtils.isBlank(riskId)){
            PreOrder preOrder = preOrderRepository.findByOrderNo(order.getOuterOrderId()).orElseThrow();
            riskId = preOrder.getRiskId();
        }
        List<UserFile> riskFiles = agreementSignRelationRepository.getUserFiles(riskId, LoanStage.RISK);
        List<UserFile> creditFiles = agreementSignRelationRepository.getUserFiles(loan.getCreditId(), LoanStage.CREDIT);
        List<UserFile> loanFiles = agreementSignRelationRepository.getUserFiles(relatedId, LoanStage.LOAN);
        List<UserFile> repayFiles = agreementSignRelationRepository.getUserFiles(relatedId, LoanStage.REPAY);
        List<List<UserFile>> lists = List.of(riskFiles, creditFiles, loanFiles, repayFiles);

        List<UserFile> files = lists.stream()
            .filter(list -> !CollectionUtils.isEmpty(list))
            .flatMap(List::stream)
            .collect(Collectors.toList());

        mergeStream(files, contractFileDTOS);

        return contractFileDTOS;
    }

    private void mergeStream(List<UserFile> userFiles, List<ContractFileDTO> contractFileDTOS) {
        userFiles.stream()
            .filter(item -> WhetherState.Y == item.getSignFinal())
            //去重复
            .collect(Collectors.toMap(UserFile::getFileType, value -> value, (v1, v2) -> {
                if (v1.getCreatedTime().isAfter(v2.getCreatedTime())) {
                    return v1;
                }
                return v2;
            }))
            .values().forEach(userFile -> {
                ContractFileDTO contractInfo = new ContractFileDTO();
                contractInfo.setAgreementName(userFile.getFileName());
                String ossUrl = fileService.getOssUrl(userFile.getOssBucket(), userFile.getOssKey());
                contractInfo.setAgreementUrl(ossUrl);
                contractFileDTOS.add(contractInfo);
            });
    }


}
