package com.maguo.loan.cash.flow.service;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileInfoDto;
import com.jinghang.capital.api.dto.loan.LoanApplyDto;
import com.jinghang.capital.api.dto.loan.LoanQueryDto;
import com.jinghang.capital.api.dto.loan.LoanResultDto;
import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.api.enums.CapitalRoute;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.convert.LoanConvert;
import com.maguo.loan.cash.flow.dto.LoanSuspendFlag;
import com.maguo.loan.cash.flow.dto.ShareAgreeNoResult;
import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.entity.CapitalConfig;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.LoanFailFollow;
import com.maguo.loan.cash.flow.entity.LoanRecord;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.QhBank;
import com.maguo.loan.cash.flow.enums.ShareResultType;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.remote.core.FinLoanService;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.LoanFailFollowRepository;
import com.maguo.loan.cash.flow.repository.LoanRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.service.agreement.AgreementService;
import com.maguo.loan.cash.flow.service.bound.CapitalCardService;
import com.maguo.loan.cash.flow.service.common.loan.LoanCommonService;
import com.maguo.loan.cash.flow.service.event.LoanResultEvent;
import com.maguo.loan.cash.flow.service.event.OrderCancelEvent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/10/9
 */
@Service
public class LoanService {

    private static final Logger logger = LoggerFactory.getLogger(LoanService.class);


    private CreditRepository creditRepository;
    private LoanRepository loanRepository;

    private MqService mqService;

    private FinLoanService finLoanService;

    private WarningService warningService;

    private AgreementService agreementService;

    private ApplicationEventPublisher eventPublisher;

    private OrderRepository orderRepository;

    private UserFileService userFileService;

    @Autowired
    private CapitalConfigRepository capitalConfigRepository;

    private PlatformOnceBoundService platformOnceBoundService;

    @Autowired
    private LoanCommonService loanCommonService;

    @Autowired
    private LockService lockService;

    @Autowired
    private CheckService checkService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private CapitalCardService capitalCardService;

    @Autowired
    private LoanFailFollowRepository loanFailFollowRepository;

    @Autowired
    private LoanRecordRepository loanRecordRepository;
    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private PreLoanService preLoanService;

    public static final int LOCK_WAIT_SECOND = 3;

    public static final int LOCK_RELEASE_SECOND = 8;

    public Loan apply(String creditId) {
        logger.info("申请放款初始化:{}", creditId);
        Credit credit = creditRepository.findById(creditId).orElseThrow();
        Order order = orderRepository.findById(credit.getOrderId()).orElseThrow();

        Locker lock = lockService.getLock("user_loan_" + order.getUserId());
        try {
            if (!lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND))) {
                // 拿不到锁
                throw new BizException(ResultCode.NO_SUBMIT_REPEAT);
            }

            if (checkService.onOrderTransitingNotOrderId(order.getUserId(), order.getFlowChannel(), order.getId())) {
                // 拿到锁, 检测在途不通过
                logger.warn("当前用户存在其它在途订单:{}", order.getUserId());
                eventPublisher.publishEvent(new OrderCancelEvent(order.getId(), "存在在途订单"));
                return null;
            }
            /**
             * 该用户其他渠道 风控通过的订单置为放款取消
             * 2025-09-04 1000321-去除放款取消逻辑
             */
//            orderService.cancelOtherOrders(order);

        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }

        // 如果已存在借据，则更新loan，否则新增
        Loan loan = loanCommonService.initLoan(creditId);

        //流量三要素验证
        boolean isPass = preLoanService.flowPreLoanCheck(order);
        if (!isPass) {
            loanCommonService.loanFail(loan);
            return loan;
        }
        LoanSuspendFlag loanSuspendFlag = LoanSuspendFlag.RECOVER;

        if (StringUtils.isEmpty(order.getProjectCode())){
            //没有项目编码，继续走路由
            // 放款中状态推送
            loanSuspendFlag = loanCommonService.suspendCheck(loan);
        }else {
            ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(order.getProjectCode());
            ProjectElementsDto elements = projectInfoVO.getElements();//项目要素
            //路由规则判断
            switch (elements.getCapitalRoute()){
                case ROUTE -> {
                    // 放款中状态推送
                    loanSuspendFlag = loanCommonService.suspendCheck(loan);
                }
                case DIRECT -> {
                    //黑暗期校验
                    boolean timeSuspendLoanFlowChannel = loanCommonService.timeSuspendLoanFlowChannel(order);
                    if (timeSuspendLoanFlowChannel){
                        //黑暗期，订单挂起
                        loanSuspendFlag = LoanSuspendFlag.SUSPEND;
                    }
                }
                default -> {
                    logger.warn("申请放款,没有对应的路由方式，loanId：[{}],orderId：[{}]",loan.getId(),order.getId());
                    warningService.warn("申请放款,没有对应的路由方式，loanId：[{}],orderId：[{}]",loan.getId(),order.getId());
                    return loan;
                }
            }
        }

        switch (loanSuspendFlag) {
            //忽略下面执行结果,直接返回
            case IGNORE -> {
                return loan;
            }
            //挂起
            case SUSPEND -> {
                //  订单挂起
                loan.setLoanState(ProcessState.SUSPEND);
                return loanRepository.save(loan);
            }
            default -> {
                //继续放款
            }
        }

        // 协议签署
        //agreementService.applySign(loan.getOrderId(), LoanStage.LOAN, loan.getBankChannel());
        // 申请放款
        mqService.submitLoanApply(loan.getId());
        return loan;
    }


    public void loanApply(String loanId) {
        Loan loan = loanRepository.findById(loanId).orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));

        //判断是否存在非失败的的放款记录
        if (loanRecordRepository.existsByLoanIdAndLoanStateNotIn(loanId, ProcessState.FAILED)) {
            warningService.warn("放款申请异常,借据[" + loanId + "]存在非失败的放款记录", logger::error);
            return;
        }

        //校验借据状态
        if (ProcessState.PROCESSING != loan.getLoanState() && ProcessState.INIT != loan.getLoanState()) {
            //借据状态异常
            warningService.warn("放款申请异常,借据[" + loanId + "]状态为" + loan.getLoanState(), logger::error);
            return;
        }

        //生成放款记录表
        LoanRecord loanRecord = genLoanRecord(loan);

        //更新放款记录id
        loan.setLoanRecordId(loanRecord.getId());
        loanRepository.save(loan);


        // 放款申请
        mqService.submitLoanRecordApply(loanRecord.getId());
    }


    public void bankLoanRecord(String loanRecordId) {
        LoanRecord loanRecord = loanRecordRepository.findById(loanRecordId).orElseThrow();

        //校验放款记录
        if (ProcessState.PROCESSING != loanRecord.getLoanState() && ProcessState.INIT != loanRecord.getLoanState()) {
            //借据状态异常
            warningService.warn("放款申请core异常,放款记录[" + loanRecordId + "]状态为" + loanRecord.getLoanState(), logger::error);
            return;
        }

        Loan loan = loanRepository.findById(loanRecord.getLoanId()).orElseThrow();
        Order order = orderRepository.findById(loanRecord.getOrderId()).orElseThrow();

        LoanApplyDto loanApplyDto = new LoanApplyDto();
        loanApplyDto.setSysId(loanRecord.getId());
        loanApplyDto.setProjectCode(order.getProjectCode());
        loanApplyDto.setSysCreditId(loanRecord.getCreditId());
        loanApplyDto.setGuaranteeCompany(loanRecord.getGuaranteeCompany());
        loanApplyDto.setLoanAmt(loanRecord.getAmount());
        loanApplyDto.setPeriods(loanRecord.getPeriods());
        loanApplyDto.setLoanApplyContractNo(loanRecord.getLoanContractNo());
        loanApplyDto.setLoanPurpose(EnumConvert.INSTANCE.toCoreApi(loanRecord.getLoanPurpose()));
        loanApplyDto.setOrderId(order.getId());
        // 检查协议签署 通过项目唯一编码id配置校验
        String projectCode = loan.getProjectCode();
        List<UserFile> userFiles = userFileService.checkMustCompleted(loanRecord.getUserId(), loanRecord.getId(), LoanStage.LOAN,
            projectCode);
        //共享协议
        ShareAgreeNoResult shareResult = shareCardAgreeNo(order, loanRecord);
        if (ShareResultType.NEED_RETRY == shareResult.getResultType()) {
            //共享协议失败  丢延迟队列重试
            mqService.submitLoanRecordApplyDelay(loanRecordId);
            return;
        }

        //共享失败
        if (ShareResultType.FAILED == shareResult.getResultType()) {
            ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(order.getProjectCode());
            ProjectElementsDto elements = projectInfoVO.getElements();//项目要素
            if (CapitalRoute.ROUTE == elements.getCapitalRoute()){
                loanRecord.setLoanState(ProcessState.FAILED);
                loanRecord.setFailReason("共享协议失败:" + shareResult.getFailReason());
                loanRecordRepository.save(loanRecord);

                loan.setLoanNo(null);
                loan.setLoanState(ProcessState.PROCESSING);
                loanRepository.save(loan);
                saveLoanFailFollow(loanRecord);
                //路由到下个资方
                loanRoute(List.of(loanRecord.getId()));
                return;
            }else {
                //只要不是路由直接失败
                loan.setLoanNo(null);
                loan.setLoanState(ProcessState.FAIL);
                loanRepository.save(loan);
                return;
            }
        }
        //放款时传递银行卡信息
        loanApplyDto.setBankCardInfo(platformOnceBoundService.obtainLoanCard(loan.getLoanCardId()));
        //loanApplyDto.setCardId(loanApplyDto.getBankCardInfo().getCardNo());
        // 部分资方 借款前需要传递借款相关协议
        if (BankChannel.CYBK == loan.getBankChannel()) {
            List<FileInfoDto> list = LoanConvert.INSTANCE.toFileInfoDto(userFiles);
            loanApplyDto.setFileInfoDtoList(list);
            loanApplyDto.setLoanApplyContractNo(loanRecordId);
        }

        logger.info("申请core放款:{}", JsonUtil.toJsonString(loanApplyDto));
        RestResult<LoanResultDto> restResult = finLoanService.loan(loanApplyDto);
        if (!restResult.isSuccess()) {
            warningService.warn("申请core放款异常:" + restResult.getMsg(), msg -> logger.error("申请core放款异常:{}", JsonUtil.toJsonString(restResult)));
        }

        LoanResultDto loanResultDto = restResult.getData();
        loanRecord.setLoanState(ProcessState.PROCESSING);
        loanRecord.setLoanNo(loanResultDto.getLoanId());
        loanRecordRepository.save(loanRecord);

        loan.setLoanState(ProcessState.PROCESSING);
        //loan.setLoanNo(loanResultDto.getLoanId());
        loanRepository.save(loan);

        // 延时查放款结果
        mqService.submitLoanRecordResultQueryDelay(loanRecordId);
    }

    private LoanRecord genLoanRecord(Loan loan) {
        LoanRecord loanRecord = LoanConvert.INSTANCE.toLoanRecord(loan);

        return loanRecordRepository.save(loanRecord);
    }


    /**
     * 共享协议
     *
     * @param order 订单
     */
    private ShareAgreeNoResult shareCardAgreeNo(Order order, LoanRecord loanRecord) {
        ShareAgreeNoResult result = new ShareAgreeNoResult();
        if (WhetherState.Y == order.getBindCapitalCardState()) {
            //已绑定资方卡
            result.setResultType(ShareResultType.SUCCEED);
            return result;
        }
        if (!QhBank.getQhBankBy(order.getBankChannel()).isNeedShareAgreeNo()) {
            //不需要共享
            result.setResultType(ShareResultType.SUCCEED);
            return result;
        }

        Credit credit = creditRepository.findById(loanRecord.getCreditId()).orElseThrow();
        //共享协议
        BindCardRecord bindCardRecord = capitalCardService.bindApply(credit);
        if (ProcessState.SUCCEED == bindCardRecord.getState()) {
            order.setBindCapitalCardState(WhetherState.Y);
            orderRepository.save(order);
            result.setResultType(ShareResultType.SUCCEED);
            return result;
        } else {
            logger.error("放款共享协议失败: loanRecordId = {},failReason = {}", loanRecord.getId(), bindCardRecord.getFailReason());
            if (StringUtil.isNotBlank(bindCardRecord.getFailReason())
                    && StringUtils.containsAny(bindCardRecord.getFailReason(),
                    "OC0051:证件号验证错误", "OC0053:姓名验证错误", "OC0070:他行卡号有误")) {
                //不需要重试
                result.setResultType(ShareResultType.FAILED);
                result.setFailReason(bindCardRecord.getFailReason());
                return result;
            }
            //需要重试
            result.setResultType(ShareResultType.NEED_RETRY);
            result.setFailReason(bindCardRecord.getFailReason());
            return result;
        }
    }


    public void bankLoanRecordResult(String loanRecordId) {
        LoanRecord loanRecord = loanRecordRepository.findById(loanRecordId).orElseThrow(() -> new BizException(ResultCode.LOAN_RECORD_NOT_EXIST));
        Loan loan = loanRepository.findById(loanRecord.getLoanId()).orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));
        Optional<CapitalConfig> byBankChannel = capitalConfigRepository.findByBankChannelAndGuaranteeCompany(loan.getBankChannel(),loan.getGuaranteeCompany());
        if (loanRecord.getLoanState() == ProcessState.SUCCEED && repayPlanRepository.countByLoanId(loan.getId()) > 0
                || loanRecord.getLoanState() == ProcessState.FAILED) {
            // 终态防止重复执行
            return;
        }
        LoanQueryDto loanQueryDto = new LoanQueryDto();
        loanQueryDto.setSysId(loanRecord.getId());
        loanQueryDto.setLoanId(loanRecord.getLoanNo());
        logger.info("查询core放款结果,param:{}", JsonUtil.toJsonString(loanQueryDto));
        RestResult<LoanResultDto> restResult = finLoanService.queryResult(loanQueryDto);
        logger.info("查询core放款结果,result:{}", JsonUtil.toJsonString(restResult));
        if (!restResult.isSuccess()) {
            throw new BizException(restResult.getMsg(), ResultCode.LOAN_ERROR);
        }
        //
        LoanResultDto resultDto = restResult.getData();
        if (!ProcessStatus.FAIL.equals(resultDto.getStatus()) && !ProcessStatus.SUCCESS.equals(resultDto.getStatus())) {
            // 不是终态
            mqService.submitLoanRecordResultQueryDelay(loanRecordId);
            return;
        }

        String loanNo = resultDto.getLoanId();
        ProcessState loanState = EnumConvert.INSTANCE.toCashState(resultDto.getStatus());

        //loan_record
        loanRecord.setLoanNo(loanNo);
        loanRecord.setLoanState(loanState);
        loanRecord.setLoanTime(resultDto.getLoanTime());
        loanRecord.setFailReason(resultDto.getFailMsg());
        loanRecord.setLoanContractNo(resultDto.getLoanContractNo());
        loanRecordRepository.save(loanRecord);

        //放款失败,若是路由对客保持处理中
        if (ProcessState.FAILED == loanState) {
            Order order = orderRepository.findById(loanRecord.getOrderId()).orElseThrow();
            ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(order.getProjectCode());
            ProjectElementsDto elements = projectInfoVO.getElements();//项目要素
            if (CapitalRoute.ROUTE == elements.getCapitalRoute()){
                //放款失败不保存资方编号
                loan.setLoanNo(null);
                loan.setLoanState(ProcessState.PROCESSING);
                loanRepository.save(loan);
                saveLoanFailFollow(loanRecord);
                loanRoute(List.of(loanRecord.getId()));
                return;
            }else {
                loan.setLoanNo(null);
                loan.setLoanState(ProcessState.FAIL);
                loanRepository.save(loan);
                saveLoanFailFollow(loanRecord);
                return;
            }
        }
        if (ProcessState.SUCCEED == loanState) {
            loan.setLoanState(ProcessState.SUCCEED);
            loan.setLoanNo(loanNo);
            loan.setLoanTime(resultDto.getLoanTime());
            loan.setLoanContractNo(resultDto.getLoanContractNo());
            loan.setBankRate(byBankChannel.get().getBankRate());
            loanRepository.save(loan);

            //放款成功,推送事件
            eventPublisher.publishEvent(new LoanResultEvent(loan.getId(), loan.getLoanState(), loan.getLoanTime(), loan.getBankChannel(),
                    loan.getFailReason()));

            //新增放款后mq推送客户信息
            //mqService.submitShmPush(loan.getId());
        }
    }


    private void saveLoanFailFollow(Loan loan) {
        Optional<LoanFailFollow> optional = loanFailFollowRepository.findById(loan.getId());
        if (optional.isEmpty()) {
            Order order = orderRepository.findById(loan.getOrderId()).orElseThrow();
            LoanFailFollow loanFailFollow = new LoanFailFollow();
            loanFailFollow.setId(loan.getId());
            loanFailFollow.setLoanId(loan.getId());
            loanFailFollow.setLoanNo(loan.getLoanNo());
            loanFailFollow.setOrderId(order.getId());
            loanFailFollow.setMobile(order.getMobile());
            loanFailFollow.setCertNo(order.getCertNo());
            loanFailFollow.setName(order.getName());
            loanFailFollow.setFlowChannel(order.getFlowChannel());
            loanFailFollow.setFailReason(loan.getFailReason());
            loanFailFollow.setBankChannel(loan.getBankChannel());
            loanFailFollow.setOuterOrderId(order.getOuterOrderId());
            loanFailFollow.setApplyTime(loan.getApplyTime());
            loanFailFollow.setIsProcess(WhetherState.N);
            loanFailFollowRepository.save(loanFailFollow);
        }
    }

    private void saveLoanFailFollow(LoanRecord loanRecord) {
        Order order = orderRepository.findById(loanRecord.getOrderId()).orElseThrow();

        LoanFailFollow loanFailFollow = loanFailFollowRepository.findById(loanRecord.getId()).orElse(new LoanFailFollow());
        loanFailFollow.setId(loanRecord.getId());
        loanFailFollow.setLoanId(loanRecord.getLoanId());
        loanFailFollow.setLoanNo(loanRecord.getLoanNo());
        loanFailFollow.setOrderId(order.getId());
        loanFailFollow.setMobile(order.getMobile());
        loanFailFollow.setCertNo(order.getCertNo());
        loanFailFollow.setName(order.getName());
        loanFailFollow.setFlowChannel(order.getFlowChannel());
        loanFailFollow.setFailReason(loanRecord.getFailReason());
        loanFailFollow.setBankChannel(loanRecord.getBankChannel());
        loanFailFollow.setOuterOrderId(order.getOuterOrderId());
        loanFailFollow.setApplyTime(loanRecord.getApplyTime());
        loanFailFollow.setIsProcess(WhetherState.N);
        loanFailFollowRepository.save(loanFailFollow);
    }

    public Loan findByOrderId(String orderId) {
        return loanRepository.findByOrderId(orderId);
    }

    public Loan findByOuterLoanIdAndFlowChannel(String outerLoanId, FlowChannel flowChannel) {
        return loanRepository.findByOuterLoanIdAndFlowChannel(outerLoanId, flowChannel)
                .orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));
    }

    public Loan findLoanByOuterLoanIdAndFlowChannel(String outerLoanId, FlowChannel flowChannel) {
        return loanRepository.findByOuterLoanIdAndFlowChannel(outerLoanId, flowChannel).orElse(null);
    }

    public Loan findById(String loadId) {
        return loanRepository.findById(loadId).orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));
    }

    public Loan saveLoan(Loan loan) {
        return loanRepository.save(loan);
    }


    @Autowired
    public void setPlatformOnceBoundService(PlatformOnceBoundService platformOnceBoundService) {
        this.platformOnceBoundService = platformOnceBoundService;
    }

    @Autowired
    public void setCreditRepository(CreditRepository creditRepository) {
        this.creditRepository = creditRepository;
    }

    @Autowired
    public void setLoanRepository(LoanRepository loanRepository) {
        this.loanRepository = loanRepository;
    }

    @Autowired
    public void setMqService(MqService mqService) {
        this.mqService = mqService;
    }

    @Autowired
    public void setFinLoanService(FinLoanService finLoanService) {
        this.finLoanService = finLoanService;
    }

    @Autowired
    public void setWarningService(WarningService warningService) {
        this.warningService = warningService;
    }

    @Autowired
    public void setEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    @Autowired
    public void setAgreementService(AgreementService agreementService) {
        this.agreementService = agreementService;
    }

    @Autowired
    public void setOrderRepository(OrderRepository orderRepository) {
        this.orderRepository = orderRepository;
    }

    @Autowired
    public void setUserFileService(UserFileService userFileService) {
        this.userFileService = userFileService;
    }


    public void loanFail(List<String> loanRecordIds) {
        logger.info("放款置为失败:{}", JsonUtil.toJsonString(loanRecordIds));

        List<LoanFailFollow> loanFailFollows = loanFailFollowRepository.findAllByIdIn(loanRecordIds);
        if (CollectionUtil.isEmpty(loanFailFollows)) {
            return;
        }
        for (LoanFailFollow loanFailFollow : loanFailFollows) {
            LoanRecord loanRecord = loanRecordRepository.findById(loanFailFollow.getId()).orElseThrow();
            Loan loan = loanRepository.findById(loanRecord.getLoanId()).orElseThrow();
            Order order = orderRepository.findById(loanRecord.getOrderId()).orElseThrow();

            if (ProcessState.FAILED != loanRecord.getLoanState()
                    || ProcessState.PROCESSING != loan.getLoanState()
                    || OrderState.LOANING != order.getOrderState()) {
                warningService.warn("借款记录:[" + loanFailFollow.getId() + "],状态异常,放款置为失败异常,loanRecordState:" + loanRecord.getLoanState()
                        + ",loanState:" + loan.getLoanState() + ",orderState:" + order.getOrderState(), logger::error);
                continue;
            }

            //判断是否存在非失败的的放款记录
            if (loanRecordRepository.existsByOrderIdAndLoanStateNotIn(order.getId(), ProcessState.FAILED)) {
                warningService.warn("放款置为失败异常,订单[" + order.getId() + "]存在非失败的放款记录", logger::error);
                continue;
            }

            loan.setLoanState(ProcessState.FAILED);
            loan = loanRepository.save(loan);

            loanFailFollow.setIsProcess(WhetherState.Y);
            loanFailFollowRepository.save(loanFailFollow);

            logger.info("置为放款失败:loanId:{}", loan.getId());
            eventPublisher.publishEvent(new LoanResultEvent(loan.getId(), loan.getLoanState(),
                    loan.getLoanTime(), loan.getBankChannel(), loan.getFailReason()));
        }
    }

    /**
     * 放款失败路由到下个资方
     *
     * @param loanRecordIds 放款记录id
     */
    public void loanRoute(List<String> loanRecordIds) {
        logger.info("放款失败资方路由:{}", JsonUtil.toJsonString(loanRecordIds));

        for (String loanRecordId : loanRecordIds) {
            Locker lock = lockService.getLock("processLoanRecord_" + loanRecordId);

            try {
                if (!lock.tryLock(Duration.ZERO, Duration.ofSeconds(LOCK_RELEASE_SECOND))) {
                    // 拿不到锁
                    logger.info("放款失败资方路由未拿到锁");
                    return;
                }

                LoanFailFollow loanFailFollow = loanFailFollowRepository.findById(loanRecordId).orElseThrow();

                if (WhetherState.Y == loanFailFollow.getIsProcess()) {
                    warningService.warn("借款记录:[" + loanFailFollow.getId() + "]已处理,忽略路由");
                    continue;
                }

                LoanRecord loanRecord = loanRecordRepository.findById(loanRecordId).orElseThrow();
                Loan loan = loanRepository.findById(loanRecord.getLoanId()).orElseThrow();
                Order order = orderRepository.findById(loanRecord.getOrderId()).orElseThrow();

                if (ProcessState.FAILED != loanRecord.getLoanState()
                        || ProcessState.PROCESSING != loan.getLoanState()
                        || OrderState.LOANING != order.getOrderState()) {
                    warningService.warn("借款记录:[" + loanFailFollow.getId() + "],状态异常,路由下个资方失败,loanRecordState:" + loanRecord.getLoanState()
                            + ",loanState:" + loan.getLoanState() + ",orderState:" + order.getOrderState(), logger::error);
                    continue;
                }

                //判断是否存在非失败的的放款记录
                if (loanRecordRepository.existsByOrderIdAndLoanStateNotIn(order.getId(), ProcessState.FAILED)) {
                    warningService.warn("放款失败路由到下个资方失败,订单[" + order.getId() + "]存在非失败的放款记录", logger::error);
                    continue;
                }

                //重置订单资方绑卡状态
                order.setBindCapitalCardState(WhetherState.N);
                orderRepository.save(order);

                //放款失败记录置为已处理
                loanFailFollow.setIsProcess(WhetherState.Y);
                loanFailFollow.setRemark("已路由到下个资方");
                loanFailFollowRepository.save(loanFailFollow);

                // 开始路由
                mqService.submitCreditRouteApply(loanFailFollow.getOrderId());
            } catch (Exception e) {
                logger.error("放款失败路由异常", e);
            } finally {
                lock.unlock();
            }
        }

    }


    /**
     * 原资方重推放款
     *
     * @param loanRecordIds 放款记录id
     */
    public void reapplyLoan(List<String> loanRecordIds) {
        logger.info("原资方重推放款:{}", JsonUtil.toJsonString(loanRecordIds));

        for (String loanRecordId : loanRecordIds) {
            Locker lock = lockService.getLock("processLoanRecord_" + loanRecordId);

            try {
                if (!lock.tryLock(Duration.ZERO, Duration.ofSeconds(LOCK_RELEASE_SECOND))) {
                    // 拿不到锁
                    logger.info("原资方重推放款未拿到锁");
                    return;
                }

                LoanFailFollow loanFailFollow = loanFailFollowRepository.findById(loanRecordId).orElseThrow();

                if (WhetherState.Y == loanFailFollow.getIsProcess()) {
                    warningService.warn("借款记录:[" + loanFailFollow.getId() + "]已处理,忽略原资方重推放款");
                    continue;
                }

                LoanRecord loanRecord = loanRecordRepository.findById(loanRecordId).orElseThrow();
                Loan loan = loanRepository.findById(loanRecord.getLoanId()).orElseThrow();
                Order order = orderRepository.findById(loanRecord.getOrderId()).orElseThrow();

                if (ProcessState.FAILED != loanRecord.getLoanState()
                        || ProcessState.PROCESSING != loan.getLoanState()
                        || OrderState.LOANING != order.getOrderState()) {
                    warningService.warn("借款记录:[" + loanFailFollow.getId() + "],状态异常,原资方重推放款失败,loanRecordState:" + loanRecord.getLoanState()
                            + ",loanState:" + loan.getLoanState() + ",orderState:" + order.getOrderState(), logger::error);
                    continue;
                }

                //判断是否存在非失败的的放款记录
                if (loanRecordRepository.existsByOrderIdAndLoanStateNotIn(order.getId(), ProcessState.FAILED)) {
                    warningService.warn("原资方重推放款失败,订单[" + order.getId() + "]存在非失败的放款记录", logger::error);
                    continue;
                }

                //放款失败记录置为已处理
                loanFailFollow.setIsProcess(WhetherState.Y);
                loanFailFollow.setRemark("原资方已重推放款");
                loanFailFollowRepository.save(loanFailFollow);

                // 原资方重推放款
                mqService.submitLoanApply(loan.getId());
            } catch (Exception e) {
                logger.error("原资方重推放款异常", e);
            } finally {
                lock.unlock();
            }
        }
    }
}
