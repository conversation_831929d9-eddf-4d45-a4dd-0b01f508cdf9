package com.maguo.loan.cash.flow.service;


import com.jinghang.capital.api.dto.FileType;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreLoanAuditRecord;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreLoanAuditRecordRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.UserDeviceRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.event.LoanResultEvent;
import com.maguo.loan.cash.flow.service.event.OrderCancelEvent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

;


/**
 * <AUTHOR>
 * @date 2023/10/9
 */
@Service
public class PreLoanService {

    private static final Logger logger = LoggerFactory.getLogger(PreLoanService.class);

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private PreLoanAuditRecordRepository preLoanAuditRecordRepository;

    @Autowired
    private UserDeviceRepository userDeviceRepository;

    @Autowired
    private MqService mqService;

    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private WarningService warningService;

    @Value("${flow.preLoan.flag:true}")
    private boolean flowPreLoanFlg;

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Autowired
    private OrderService orderService;

    @Autowired
    private PreOrderRepository preOrderRepository;

    private static final List<FileType> ID_FILE_TYPES = List.of(FileType.ID_FACE, FileType.ID_NATION, FileType.ID_HEAD);


    public void preLoanApply(String orderId) {
        //二次审核统一调整为订单路由前
        Order order = orderRepository.findById(orderId).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));

        if (order.getOrderState() != OrderState.SUSPENDED) {
            logger.info("二次征信申请订单状态为[{}]，不处理", order.getOrderState());
            return;
        }

        PreLoanAuditRecord preLoanAuditRecord = preLoanAuditRecordRepository.findByOrderId(orderId).orElse(new PreLoanAuditRecord());

        String riskId = order.getRiskId();
        if (StringUtils.isBlank(riskId)){
            PreOrder preOrder = preOrderRepository.findByOrderNo(order.getOuterOrderId()).orElseThrow();
            riskId = preOrder.getRiskId();
        }
        UserRiskRecord userRiskRecord = userRiskRecordRepository.findById(riskId)
                .orElseThrow(() -> new BizException(ResultCode.RISK_RECORD_NOT_EXIST_ERROR));

        AuditState approveResult = preLoanAuditRecord.getApproveResult();
        if (approveResult == AuditState.AUDITING || approveResult == AuditState.PASS || approveResult == AuditState.REJECT) {
            logger.info("贷前审批orderId: [{}] , 状态为: [{}],不处理", orderId, approveResult);
            return;
        }
        preLoanAuditRecord = initPreLoanAudit(order, userRiskRecord.getId(), preLoanAuditRecord);

    }


    public boolean flowPreLoanCheck(Order order) {
        String riskId = order.getRiskId();
        if (StringUtils.isBlank(riskId)){
            PreOrder preOrder = preOrderRepository.findByOrderNo(order.getOuterOrderId()).orElseThrow();
            riskId = preOrder.getRiskId();
        }
        UserRiskRecord userRiskRecord = userRiskRecordRepository.findById(riskId)
                .orElseThrow(() -> new BizException(ResultCode.RISK_RECORD_NOT_EXIST_ERROR));
        PreLoanAuditRecord preLoanAuditRecord = preLoanAuditRecordRepository.findByOrderId(order.getId()).orElse(new PreLoanAuditRecord());

        if (preLoanAuditRecord.getApproveResult() == AuditState.PASS) {
            return true;
        } else if (preLoanAuditRecord.getApproveResult() == AuditState.REJECT) {
            return false;
        }

        preLoanAuditRecord = initPreLoanAudit(order, userRiskRecord.getId(), preLoanAuditRecord);

        try {
            //放款不加风控逻辑
            //流量调用风控准入事件
            preLoanAuditRecord.setApproveResult(AuditState.PASS);

        } catch (Exception e) {
            warningService.warn("微言准入事件请求异常,loanId:" + order.getId(), "," + e.getMessage());

            logger.error("微言准入事件请求异常,loanId:{}", order.getId(), e);
            //异常情况都通过
            preLoanAuditRecord.setApproveResultCode("");
            preLoanAuditRecord.setApproveResult(AuditState.PASS);
            preLoanAuditRecord.setPassTime(LocalDateTime.now());
            preLoanAuditRecord.setRemark("异常置为通过:" + e.getMessage());
        }
        preLoanAuditRecord = preLoanAuditRecordRepository.save(preLoanAuditRecord);
        return preLoanAuditRecord.getApproveResult() == AuditState.PASS;
    }


    private void loanFail(Loan loan, PreLoanAuditRecord preLoanAuditRecord) {
        loan.setLoanState(ProcessState.FAILED);
        loan = loanRepository.save(loan);
        eventPublisher.publishEvent(new LoanResultEvent(loan.getId(), loan.getLoanState(), loan.getLoanTime(), loan.getBankChannel(), loan.getFailReason()));
    }


    private PreLoanAuditRecord initPreLoanAudit(Order order, String riskId, PreLoanAuditRecord preLoanAuditRecord) {
        preLoanAuditRecord.setLoanId(null);
        preLoanAuditRecord.setOrderId(order.getId());
        preLoanAuditRecord.setCreditId(riskId);
        preLoanAuditRecord.setUserId(order.getUserId());
        preLoanAuditRecord.setFlowChannel(order.getFlowChannel());
        preLoanAuditRecord.setApproveResult(AuditState.INIT);
        preLoanAuditRecord.setApplyTime(LocalDateTime.now());
        return preLoanAuditRecordRepository.save(preLoanAuditRecord);
    }


    public void preLoanAuditResult(String id) {
        PreLoanAuditRecord preLoanAuditRecord = preLoanAuditRecordRepository.findById(id).orElseThrow(() -> new BizException(ResultCode.PRE_LOAN_NOT_EXIST));

        Order order = orderRepository.findById(preLoanAuditRecord.getOrderId()).orElseThrow();
        if (order.getOrderState() != OrderState.SUSPENDED) {
            logger.info("二次征信查询订单状态为{}，不处理", order.getOrderState());
            return;
        }
        AuditState approveResult = preLoanAuditRecord.getApproveResult();
        if (approveResult != AuditState.AUDITING) {
            return;
        }

        preLoanAuditRecord = preLoanAuditRecordRepository.save(preLoanAuditRecord);
        if (preLoanAuditRecord.getApproveResult() == AuditState.PASS) {
            //激活订单
            orderService.orderRoute(order);
        } else {
            //订单置为放款失败
            eventPublisher.publishEvent(new OrderCancelEvent(order.getId(), "放款失败"));
        }


    }


}
