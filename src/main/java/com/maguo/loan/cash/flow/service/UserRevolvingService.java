package com.maguo.loan.cash.flow.service;


import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.dto.UserRevolvingRangeDTO;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.RevolvingStrategyConfig;
import com.maguo.loan.cash.flow.entity.RevolvingStrategyRelate;
import com.maguo.loan.cash.flow.entity.RiskRevolvingRecord;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserRelateRecord;
import com.maguo.loan.cash.flow.entity.UserRevolving;
import com.maguo.loan.cash.flow.entity.UserRevolvingRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.enums.AmountType;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.enums.RevolvingAdjustType;
import com.maguo.loan.cash.flow.enums.RevolvingState;
import com.maguo.loan.cash.flow.enums.RightsLevel;
import com.maguo.loan.cash.flow.enums.RightsPayType;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.RevolvingStrategyConfigRepository;
import com.maguo.loan.cash.flow.repository.RevolvingStrategyReleateRepository;
import com.maguo.loan.cash.flow.repository.RiskRevolvingRecordRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserReleateRecordRepository;
import com.maguo.loan.cash.flow.repository.UserRevolvingRecordRepository;
import com.maguo.loan.cash.flow.repository.UserRevolvingRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.util.AmountUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Random;

/**
 * <AUTHOR>
 * @since 2025-02-21
 */
@Service
public class UserRevolvingService {
    private static final Logger logger = LoggerFactory.getLogger(UserRevolvingService.class);

    @Autowired
    private UserRevolvingRepository userRevolvingRepository;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private RevolvingStrategyConfigRepository revolvingStrategyConfigRepository;

    @Autowired
    private RevolvingStrategyReleateRepository revolvingStrategyReleateRepository;

    @Autowired
    private RiskRevolvingRecordRepository riskRevolvingRecordRepository;

    @Autowired
    private UserReleateRecordRepository userReleateRecordRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Autowired
    private UserRevolvingRecordRepository userRevolvingRecordRepository;

    @Autowired
    private PreOrderRepository preOrderRepository;


    @Autowired
    private MqService mqService;


    private static final List<OrderState> ORDER_STATE_LIST = List.of(OrderState.INIT, OrderState.AUDIT_PASS, OrderState.CREDITING,
            OrderState.CREDIT_PASS, OrderState.LOANING, OrderState.SUSPENDED, OrderState.LOAN_PASS);

    private static final int MAX_RANDOM_LENGTH = 100;
    @Autowired
    private CheckService checkService;

    public UserRevolvingRangeDTO getUserRevolvingRange(String userId, boolean useFrontAmount) {
        UserRevolving userRevolving = userRevolvingRepository.findById(userId).orElseThrow(() -> new RuntimeException("尚未获得额度"));
        if (userRevolving.getRevolvingState() == RevolvingState.INVALID) {
            throw new RuntimeException("额度不可用");
        }
        // 总额度
        BigDecimal totalAmount = userRevolving.getApproveAmount();
        // 分箱额度
        BigDecimal showAmount = BigDecimal.ZERO;
        boolean isBinning = false;
        if (StringUtil.isNotBlank(userRevolving.getStrategyRelateId())) {
            Optional<RevolvingStrategyRelate> revolvingStrategyBinning = revolvingStrategyReleateRepository.findById(userRevolving.getStrategyRelateId());
            if (revolvingStrategyBinning.isPresent()) {
                showAmount = revolvingStrategyBinning.get().getFrontAmount();
                isBinning = true;
            }
        }
        totalAmount = useFrontAmount && isBinning ? showAmount : totalAmount;
        // 在途 在贷 额度
        List<Order> orderList = orderRepository.findAllByUserIdAndAmountTypeAndOrderStateIn(userId, AmountType.REVOLVING, ORDER_STATE_LIST);
        // 过滤掉未提交的订单
        orderList = orderList.stream().filter(x -> x.getOrderSubmitState().equals(WhetherState.Y)).toList();
        BigDecimal usedAmount = orderList.stream().map(Order::getApproveAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 已还款总额度
        List<String> orderIds = orderList.stream().filter(x -> x.getOrderState() == OrderState.LOAN_PASS).map(Order::getId).toList();
        List<Loan> loanList = loanRepository.findAllByOrderIdIn(orderIds);
        List<String> loanIds = loanList.stream().map(Loan::getId).toList();
        List<RepayPlan> repaidList = repayPlanRepository.findAllByLoanIdInAndCustRepayState(loanIds, RepayState.REPAID);
        BigDecimal repaidAmount = repaidList.stream().map(RepayPlan::getPrincipalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 计算可用额度
        BigDecimal availableAmount = AmountUtil.sum(AmountUtil.subtract(totalAmount, usedAmount), repaidAmount);
        availableAmount = availableAmount.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : availableAmount;
        return calRevolvingRange(availableAmount, totalAmount, showAmount);
    }

    private UserRevolvingRangeDTO calRevolvingRange(BigDecimal availableAmount, BigDecimal totalAmount, BigDecimal showAmount) {
        BigDecimal min = BigDecimal.ZERO;
        BigDecimal max = availableAmount;

        if (availableAmount.compareTo(min) < 0) {
            min = BigDecimal.ZERO;
            max = BigDecimal.ZERO;
        }
        UserRevolvingRangeDTO rangeDTO = new UserRevolvingRangeDTO();
        rangeDTO.setMin(min);
        rangeDTO.setMax(max);
        rangeDTO.setTotalAmount(totalAmount);
        rangeDTO.setBinningAmount(showAmount);
        rangeDTO.setAvailableAmount(availableAmount);
        return rangeDTO;
    }

    public void updateUserRevolvingAmount(String userId) {
        if (StringUtil.isBlank(userId)) {
            return;
        }
        // 存在处理中的循环额度订单
        if (checkService.onOrderTransitingByAmountType(userId, AmountType.REVOLVING)) {
            return;
        }
        Optional<UserRevolving> revolvingOptional = userRevolvingRepository.findById(userId);
        if (revolvingOptional.isPresent()) {
            UserRevolving revolving = revolvingOptional.get();
            if (revolving.getRevolvingState() == RevolvingState.INVALID) {
                return;
            }
            boolean amountExpired = LocalDateTime.now().isAfter(revolving.getAmountExpireTime());
            boolean revolvingExpired = LocalDateTime.now().isAfter(revolving.getRevolvingExpireTime());
            if (revolvingExpired || amountExpired) {
                UserInfo userInfo = userInfoRepository.findUserInfoById(userId);
                revolving.setRevolvingState(RevolvingState.UPDATING);
                // 额度标签过期

                RiskRevolvingRecord riskRevolvingRecord = new RiskRevolvingRecord();
                riskRevolvingRecord.setUserId(userId);
                riskRevolvingRecord.setCertNo(userInfo.getCertNo());
                riskRevolvingRecord.setApproveResult(AuditState.INIT);
                riskRevolvingRecord = riskRevolvingRecordRepository.save(riskRevolvingRecord);
                mqService.submitRevolvingAmountUpdateApply(riskRevolvingRecord.getId());

                userRevolvingRepository.save(revolving);
            }
        }
    }

    public BigDecimal revolvingReleateHandler(BigDecimal amount, UserRevolving userRevolving, boolean isFirst) {
        logger.info("循环额度分箱处理");
        BigDecimal frontAmount = BigDecimal.ZERO;
        // 原来的额度
        BigDecimal originAmount = isFirst ? BigDecimal.ZERO : new BigDecimal(userRevolving.getApproveAmount().toString());

        List<RevolvingStrategyConfig> revolvingStrategyConfigList = revolvingStrategyConfigRepository.findAllByStrategyState(WhetherState.Y);
        if (!revolvingStrategyConfigList.isEmpty()) {
            Optional<RevolvingStrategyConfig> first = revolvingStrategyConfigList.stream()
                    .filter(x ->
                            BigDecimal.valueOf(x.getAmountLower()).compareTo(amount) <= 0
                                    && BigDecimal.valueOf(x.getAmountUpper()).compareTo(amount) >= 0
                    )
                    .findFirst();
            if (first.isPresent()) {
                RevolvingStrategyConfig revolvingStrategyConfig = first.get();
                List<RevolvingStrategyRelate> releateList = revolvingStrategyReleateRepository.findAllByStrategyId(revolvingStrategyConfig.getId());
                // 随机比例
                BigDecimal random = BigDecimal.valueOf(new Random().nextInt(MAX_RANDOM_LENGTH));
                BigDecimal current = BigDecimal.ZERO;
                for (RevolvingStrategyRelate releate : releateList) {
                    if (random.compareTo(current) >= 0 && random.compareTo(current.add(releate.getRatio())) <= 0) {
                        frontAmount = releate.getFrontAmount();
                        userRevolving.setStrategyRelateId(releate.getId());
                        UserRelateRecord releateRecord = new UserRelateRecord();
                        releateRecord.setUserId(userRevolving.getId());
                        releateRecord.setStrategyRelateId(releate.getId());
                        releateRecord.setStrategyId(releate.getStrategyId());
                        releateRecord.setStrategyRelateCode(releate.getRelateCode());
                        releateRecord = userReleateRecordRepository.save(releateRecord);
                        userRevolving.setRelateRecordId(releateRecord.getId());
                        break;
                    }
                    current = current.add(releate.getRatio());
                }
            }
        }

        return amount;
    }

    public void rightsChange(Order order) {
        if (order.getAmountType() != AmountType.REVOLVING) {
            return;
        }
        if (order.getApproveRights() == RightsLevel.NONE) {
            return;
        } else {
            if (order.getRightsMarking() == WhetherState.N) {
                return;
            }
            String riskId = order.getRiskId();
            if (StringUtils.isBlank(riskId)){
                PreOrder preOrder = preOrderRepository.findByOrderNo(order.getOuterOrderId()).orElseThrow();
                riskId = preOrder.getRiskId();
            }
            UserRiskRecord userRiskRecord = userRiskRecordRepository.findById(riskId).orElseThrow();
            if (userRiskRecord.getRightsPayType() != RightsPayType.SUBSCRIBE) {
                return;
            }
        }
        Optional<UserRevolving> revolvingOptional = userRevolvingRepository.findById(order.getUserId());
        if (revolvingOptional.isPresent()) {
            UserRevolving revolving = revolvingOptional.get();
            if (StringUtil.isNotBlank(revolving.getStrategyRelateId())) {
                Optional<RevolvingStrategyRelate> strategyReleateOptional = revolvingStrategyReleateRepository.findById(revolving.getStrategyRelateId());
                if (strategyReleateOptional.isPresent()) {
                    RevolvingStrategyRelate releate = strategyReleateOptional.get();
                    BigDecimal changeAmount;
                    if (order.getRightsMarking() == WhetherState.Y) {
                        changeAmount = AmountUtil.subtract(revolving.getApproveAmount(), releate.getFrontAmount());
                    } else {
                        changeAmount = AmountUtil.subtract(releate.getFrontAmount(), revolving.getApproveAmount());
                    }
                    if (changeAmount != null && changeAmount.compareTo(BigDecimal.ZERO) != 0) {
                        UserRevolvingRecord revolvingRecord = new UserRevolvingRecord();
                        revolvingRecord.setUserId(revolving.getId());
                        revolvingRecord.setAdjustType(RevolvingAdjustType.MARKETING_ADJUST_AMOUNT);
                        revolvingRecord.setAdjustAmount(changeAmount);
                        revolvingRecord.setAdjustTime(LocalDateTime.now());
                        userRevolvingRecordRepository.save(revolvingRecord);
                    }
                }
            }
        }
    }


}
