package com.maguo.loan.cash.flow.service.listener;


import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecordExternal;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.jinghang.cash.api.enums.RiskModelChannel;
import com.maguo.loan.cash.flow.repository.UserRiskRecordExternalRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.RiskService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Optional;

/**
 * 内部风控监听
 */
@Component
public class RiskApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(RiskApplyListener.class);

    private RiskService riskService;

    private OrderService orderService;

    @Value("${qh.risk.suspend:false}")
    private Boolean riskSuspend;

    @Autowired
    private UserRiskRecordRepository riskRecordRepository;
    @Autowired
    private UserRiskRecordExternalRepository recordExternalRepository;

    @Autowired
    private UserRiskRecordExternalRepository RiskRecordExternalRepository;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    public RiskApplyListener(MqService mqService, WarningService mqWarningService, RiskService riskService, OrderService orderService) {
        super(mqService, mqWarningService);
        this.riskService = riskService;
        this.orderService = orderService;
    }

    @RabbitListener(queues = RabbitConfig.Queues.RISK_APPLY)
    public void listenRiskApply(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("开始处理风控申请, riskId: [{}]", riskId);

        try {
            UserRiskRecord record = riskService.findPlatformRiskRecord(riskId);
            if (record == null) {
                logger.warn("UserRiskRecord用户风控记录不存在, riskId: [{}]", riskId);
                return;
            }
            String projectCode = record.getProjectCode();
            ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(projectCode);
            logger.info("项目信息日志输出：[{}]，projectCode：[{}]", JsonUtil.toJsonString(projectInfoVO),projectCode);
            RiskModelChannel riskModelChannel = projectInfoVO.getElementsExt().getRiskModelChannel();
            if (riskModelChannel == null) {
                logger.warn("风险模型渠道未配置, projectCode: [{}], riskId: [{}]", projectCode, riskId);
                return;
            }

            if (RiskModelChannel.BW.equals(riskModelChannel)) {
                riskApplyBW(message, channel);
            } else {
                riskApply(message, channel);
            }
        } catch (Exception e) {
            logger.error("处理风控申请异常, riskId: [{}]", riskId, e);
            getMqWarningService().warn("处理风控申请异常, riskId: " + riskId + ", 原因: " + e.getMessage());
        }
    }

    private void riskApply(Message message, Channel channel) {

        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("申请风控, riskId: [{}]", riskId);
            UserRiskRecord record = riskService.findPlatformRiskRecord(riskId);
            //todo 判断 record 是否为空
            //todo 如果不为空，则为内部风控数据
            //todo 如果为空，查询外部风控记录表（riskService.findPlatformRiskRecordExternal），如果外部风控记录数据不为空，则为外部风控数据
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (AuditState.REJECT == record.getApproveResult()) {
                logger.warn("风控记录,riskId: [{}],状态为已拒绝,忽略此消息", riskId);
                return;
            }

            //风控挂起
            if (riskSuspend) {
                //todo 需要上方查出的结果判断保存风控记录挂起状态
                record.setApproveResult(AuditState.SUSPEND);
                riskRecordRepository.save(record);
                return;
            }
            //todo platformRisk 有两个
            //todo 根据riskIdc查 preOrder信息，获取projectCode
            //todo 根据projectCode结果，判断是调用内部风控，参数（UserRiskRecord）；还是外部风控，参数（UserRiskRecordExternal）
            riskService.platformRisk(record);

        } catch (Exception e) {
            processException(riskId, message, e, "内部风控异常", getMqService()::submitRiskApplyDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }

    private void riskApplyBW(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("申请外部风控, riskId: [{}]", riskId);
            UserRiskRecordExternal record = riskService.findPlatformRiskRecordExternal(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("外部风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (AuditState.REJECT == record.getApproveResult()) {
                logger.warn("外部风控记录,riskId: [{}],状态为已拒绝,忽略此消息", riskId);
                return;
            }

            //风控挂起
            if (riskSuspend) {
                record.setApproveResult(AuditState.SUSPEND);
                recordExternalRepository.save(record);
                return;
            }

            riskService.platformRisk(record);

        } catch (Exception e) {
            processException(riskId, message, e, "外部风控异常", getMqService()::submitRiskApplyOutDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }


    @RabbitListener(queues = RabbitConfig.Queues.RISK_APPLY_OUT)
    public void listenRiskApplyOut(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("申请外部风控, riskId: [{}]", riskId);
            UserRiskRecordExternal record = riskService.findPlatformRiskRecordExternal(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("外部风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (AuditState.REJECT == record.getApproveResult()) {
                logger.warn("外部风控记录,riskId: [{}],状态为已拒绝,忽略此消息", riskId);
                return;
            }

            //风控挂起
            if (riskSuspend) {
                record.setApproveResult(AuditState.SUSPEND);
                recordExternalRepository.save(record);
                return;
            }

            riskService.platformRisk(record);

        } catch (Exception e) {
            processException(riskId, message, e, "外部风控异常", getMqService()::submitRiskApplyOutDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }

    @RabbitListener(queues = RabbitConfig.Queues.RISK_LOAN_APPLY)
    public void listenRiskLoanApply(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("开始处理放款风控申请, riskId: [{}]", riskId);

        try {
            Optional<UserRiskRecord> record = userRiskRecordRepository.findById(riskId);
            String projectCode = record.get().getProjectCode();
            ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(projectCode);
            if (Objects.isNull(projectInfoVO)) {
                logger.warn("Order订单风控记录不存在, riskId: [{}]", riskId);
                return;
            }

            RiskModelChannel riskModelChannel = projectInfoVO.getElementsExt().getRiskModelChannel();
            if (riskModelChannel == null) {
                logger.warn("风险模型渠道未配置, projectCode: [{}], riskId: [{}]", projectCode, riskId);
                return;
            }

            if (riskModelChannel.equals(RiskModelChannel.BW)) {
                baiWeiRiskLoanApply(message, channel);
            } else {
                riskLoanApply(message, channel);
            }
        } catch (Exception e) {
            logger.error("处理放款风控申请异常, riskId: [{}]", riskId, e);
            getMqWarningService().warn("处理放款风控申请异常, riskId: " + riskId + ", 原因: " + e.getMessage());
        }
    }

    private void riskLoanApply(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("申请支用风控, riskId: [{}]", riskId);
            UserRiskRecord record = riskService.findPlatformRiskRecord(riskId);
            //todo 判断 record 是否为空
            //todo 如果不为空，则为内部风控数据
            //todo 如果为空，查询外部风控记录表（riskService.findPlatformRiskRecordExternal），如果外部风控记录数据不为空，则为外部风控数据
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (AuditState.REJECT == record.getApproveResult()) {
                logger.warn("风控记录,riskId: [{}],状态为已拒绝,忽略此消息", riskId);
                return;
            }

            //风控挂起
            if (riskSuspend) {
                //todo 需要上方查出的结果判断保存风控记录挂起状态
                record.setApproveResult(AuditState.SUSPEND);
                riskRecordRepository.save(record);
                return;
            }
            //todo platformRisk 有两个
            //todo 根据riskIdc查 Order信息，获取projectCode
            //todo 需要根据上方查出结果，判断是调用内部风控方法（platformLoanRisk）；还是外部风控方法（baiWeiPlatformLoanRisk）
            riskService.platformLoanRisk(record);

        } catch (Exception e) {
            processException(riskId, message, e, "内部风控异常", getMqService()::submitRiskLoanApplyDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }

    private void baiWeiRiskLoanApply(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("百维：申请支用风控, riskId: [{}]", riskId);
            UserRiskRecordExternal record = riskService.findPlatformRiskRecordExternal(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (AuditState.REJECT == record.getApproveResult()) {
                logger.warn("风控记录,riskId: [{}],状态为已拒绝,忽略此消息", riskId);
                return;
            }

            //风控挂起
            if (riskSuspend) {
                record.setApproveResult(AuditState.SUSPEND);
                RiskRecordExternalRepository.save(record);
                return;
            }
            if (FlowChannel.FQLQY001.equals(record.getFlowChannel())){
                riskService.baiWeiPlatformLoanRisk(record);
            }



        } catch (Exception e) {
            processException(riskId, message, e, "百维：申请支用风控异常", getMqService()::submitBaiWeiRiskLoanApplyDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }

    @RabbitListener(queues = RabbitConfig.Queues.BW_RISK_LOAN_APPLY)
    public void listenBaiWeiRiskLoanApply(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("百维：申请支用风控, riskId: [{}]", riskId);
            UserRiskRecordExternal record = riskService.findPlatformRiskRecordExternal(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (AuditState.REJECT == record.getApproveResult()) {
                logger.warn("风控记录,riskId: [{}],状态为已拒绝,忽略此消息", riskId);
                return;
            }

            //风控挂起
            if (riskSuspend) {
                record.setApproveResult(AuditState.SUSPEND);
                RiskRecordExternalRepository.save(record);
                return;
            }
            if (FlowChannel.FQLQY001.equals(record.getFlowChannel())){
                riskService.baiWeiPlatformLoanRisk(record);
            }



        } catch (Exception e) {
            processException(riskId, message, e, "百维：申请支用风控异常", getMqService()::submitBaiWeiRiskLoanApplyDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }


    @RabbitListener(queues = RabbitConfig.Queues.ARTIFICIAL_RISK_APPLY)
    public void listenArtificialRiskApply(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("人工申请风控:{}", riskId);
            UserRiskRecord record = riskService.findPlatformRiskRecord(riskId);

            riskService.platformRisk(record);
        } catch (Exception e) {
            logger.error("人工申请风控异常:", e);
            getMqWarningService().warn("人工申请风控异常,riskId" + riskId + ":" + e.getMessage());
        } finally {
            ackMsg(riskId, message, channel);
        }
    }
}
