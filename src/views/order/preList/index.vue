<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="手机号" prop="mobile" width="150px">
        <el-input v-model="queryParams.mobile" placeholder="输入手机号" clearable size="small" />
      </el-form-item>
      <el-form-item label="身份证号" prop="certNo" width="150px">
        <el-input v-model="queryParams.certNo" placeholder="输入身份证号" clearable size="small" />
      </el-form-item>
      <el-form-item label="流量方" prop="flowChannel" width="150px">
        <el-select v-model="queryParams.flowChannel" placeholder="流量方" style="width: 140px" clearable>
          <el-option v-for="item in flowChannelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="日期">
        <el-date-picker v-model="dataVal" size="small" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
          :picker-options="pickerOptionsDefault"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" round icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" border="border" :data="list">
      <el-table-column label="姓名" prop="name" align="center" />
      <el-table-column label="身份证号" prop="certNo" align="center" />
      <el-table-column label="手机号" prop="mobile" align="center" />
      <el-table-column label="进件渠道" prop="flowChannel" align="center" :formatter="flowChannelFormat" />
      <el-table-column label="进件单号" prop="orderNo" align="center" />
      <el-table-column label="风控ID" prop="riskId" align="center" />
      <el-table-column label="风控失败原因" prop="remark" align="center" />
      <el-table-column label="进件时间" prop="applyTime" align="center" />
      <el-table-column label="创建时间" prop="createdTime" align="center" />
      <el-table-column label="更新时间" prop="updatedTime" align="center" />
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { flowChannelQuery, preOrderFailList } from "@/api/order";

export default {
  name: "OrderPreList",
  data() {
    return {
      flowChannelOptions: [],
      loading: false,
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      list: [],
      dataVal: [],
      pickerOptionsDefault: {
        onPick: ({ maxDate, minDate }) => {
          //当我们选择两个值的时候，就认为用户已经选择完毕
          if (maxDate != null && minDate != null) {
            this.repayDateBegin = maxDate;
            this.repayDateEnd = minDate;
          }
        },
        disabledDate: time => {
          let maxDate = this.maxDate;
          let minDate = this.minDate;
          if (maxDate != null && minDate != null) {
            let days = maxDate.getTime() - minDate.getTime();
            //计算完之后必须清除，否则选择器一直处于禁止选择的状态
            this.maxDate = null;
            this.minDate = null;
            return parseInt(days / (1000 * 60 * 60 * 24)) > 30;
          } else {
            //设置当前时间后的时间不可选
            return time.getTime() > Date.now();
          }
        }
      },
    };
  },
  created() {
    flowChannelQuery().then((res) => {
      let arr = [];
      for (let key in res.data) {
        arr.push({
          label: res.data[key],
          value: key,
        });
      }
      this.flowChannelOptions = arr;
    });

    // this.getList();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      if (this.dataVal && this.dataVal.length === 2) {
        this.queryParams.applyTimeStart = this.dataVal[0];
        this.queryParams.applyTimeEnd = this.dataVal[1];
      } else {
        this.queryParams.applyTimeStart = undefined;
        this.queryParams.applyTimeEnd = undefined;
      }

      preOrderFailList(this.queryParams).then((res) => {
        this.list = res.data.list
        this.total = res.data.total
      }).finally(() => {
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.flowChannel) {
        if (!this.dataVal || (this.dataVal && this.dataVal.length === 0)) {
          this.$message.error('请选择日期')
          return
        }
      }

      if (this.dataVal && this.dataVal.length === 2) {
        const days = this.daysBetween(this.dataVal[0], this.dataVal[1]);
        if (days > 30) {
          this.$message.error('开始日期到结束日期不能超过30天')
          return
        }
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },

    flowChannelFormat({ flowChannel }) {
      const obj = this.flowChannelOptions.find(
        (item) => item.value === flowChannel
      );
      return obj ? obj.label : "--";
    },

    daysBetween(date1, date2) {
      const oneDay = 24 * 60 * 60 * 1000; // 每天的毫秒数
      const firstDate = new Date(date1);
      const secondDate = new Date(date2);

      // 计算时间差
      const diff = Math.abs(firstDate - secondDate);

      // 返回差值除以每天的毫秒数
      return Math.round(diff / oneDay);
    }
  },
};
</script>
